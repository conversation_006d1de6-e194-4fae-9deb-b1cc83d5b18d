name: Status Checks

on:
  pull_request:
    branches:
      - main
      - production
  push:
    branches:
      - main
      - production

jobs:
  # This job aggregates all the required checks
  all-checks:
    runs-on: ubuntu-24.04
    name: All Required Checks
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run all quality checks
        run: |
          echo "Running type checking..."
          npm run type-check
          
          echo "Running linting..."
          npm run lint:ci
          
          echo "Running format check..."
          npm run format:check
          
          echo "Building application..."
          npm run build
          
          echo "All checks passed! ✅"

      - name: Summary
        run: |
          echo "## ✅ All Quality Checks Passed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ TypeScript compilation" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ ESLint checks" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Prettier formatting" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Build successful" >> $GITHUB_STEP_SUMMARY

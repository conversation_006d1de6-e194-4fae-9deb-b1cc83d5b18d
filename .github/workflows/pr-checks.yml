name: Pull Request - Quality Checks

on:
  pull_request:
    branches:
      - main
      - production
    types: [opened, synchronize, reopened, ready_for_review]

jobs:
  quality-check:
    runs-on: ubuntu-24.04
    name: Quality Assurance
    
    # Skip checks for draft PRs unless they're marked as ready for review
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type checking
        run: npm run type-check

      - name: Run linting
        run: npm run lint:ci

      - name: Check code formatting
        run: npm run format:check

      - name: Build application
        run: npm run build

      - name: Run tests (if available)
        run: npm test --if-present
        continue-on-error: true

  # Optional: Add a job to check for common issues
  security-check:
    runs-on: ubuntu-24.04
    name: Security & Dependency Check
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=high
        continue-on-error: true

      - name: Check for outdated dependencies
        run: npm outdated
        continue-on-error: true

name: Testnet - Quality Check, Build and Deploy

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    types: [opened, synchronize, reopened, ready_for_review]

jobs:
  quality-check:
    runs-on: ubuntu-24.04
    name: Quality Assurance
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type checking
        run: npm run type-check

      - name: Run linting
        run: npm run lint:ci

      - name: Check code formatting
        run: npm run format:check

      - name: Build application
        run: npm run build

  build-and-push:
    runs-on: ubuntu-24.04
    name: Build and Deploy
    needs: quality-check
    # Only run deployment on pushes to main, not on PRs
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run Database Migrations
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NODE_TLS_REJECT_UNAUTHORIZED: 0
        run: npx drizzle-kit push --force

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set Image Name
        run: echo IMAGE_REPOSITORY=$(echo "${{ github.repository }}" | tr '[:upper:]' '[:lower:]') >> $GITHUB_ENV

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ghcr.io/${{ env.IMAGE_REPOSITORY }}:testnet
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-region: us-east-1
          role-to-assume: ${{ secrets.AWS_IAM_ROLE_ARN }}

      - name: Deploy new ECS Task
        run: aws ecs update-service --cluster testnet-cluster --service il-token-api --task-definition testnet-il-token-api --force-new-deployment

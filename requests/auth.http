### Variables
@baseUrl = http://localhost:3001
@contentType = application/json

### Authentication - Copy values from auth-env.http when needed
### For security, actual cookie values are stored in auth-env.http (gitignored)
### Uncomment and paste your cookie values below when testing authenticated endpoints:
@sessionToken = XXXXXXX
@sessionData = XXXXXXXXX

### Settings (cookie file is unreliable, use variables above instead)
# @cookieFile = ./requests/cookies.txt

### Send Email OTP (Better Auth)
POST {{baseUrl}}/auth/email-otp/send-verification-otp
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "type": "sign-in"
}

### Sign In with Email OTP (Better Auth)
POST {{baseUrl}}/auth/sign-in/email-otp
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "otp": "856721"
}

### Send Phone OTP
POST {{baseUrl}}/auth/phone-otp/send
Content-Type: {{contentType}}

{
  "phoneNumber": "+16508238624"
}

### Link Phone with Session (requires session cookie)
POST {{baseUrl}}/auth/phone-otp/link-with-session
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "phoneNumber": "+16508238624",
  "otp": "067161"
}

### Get Current User (requires authentication)
GET {{baseUrl}}/auth/session/user

### Get Current User (requires authentication)
GET {{baseUrl}}/auth/session/user
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

### Sign Out (requires authentication)
POST {{baseUrl}}/auth/session/signout
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Test Flow Example:
### 1. First, send email OTP
# POST {{baseUrl}}/auth/email-otp/send-verification-otp
# Content-Type: {{contentType}}
# 
# {
#   "email": "<EMAIL>"
# }

### 2. Then sign in with the OTP you received
# POST {{baseUrl}}/auth/sign-in/email-otp
# Content-Type: {{contentType}}
# 
# {
#   "email": "<EMAIL>",
#   "otp": "the-6-digit-code-from-email"
# }

### 3. After successful sign-in, you can get current user (session cookie will be set automatically)
# GET {{baseUrl}}/auth/session/user

### 4. Optionally, send phone OTP and link it to your session
# POST {{baseUrl}}/auth/phone-otp/send
# Content-Type: {{contentType}}
# 
# {
#   "phoneNumber": "+1234567890"
# }

### 5. Link the phone number after receiving SMS OTP
# POST {{baseUrl}}/auth/phone-otp/link-with-session
# Content-Type: {{contentType}}
# 
# {
#   "phoneNumber": "+1234567890",
#   "otp": "the-6-digit-sms-code"
# }

### 6. Finally, sign out when done testing
# POST {{baseUrl}}/auth/session/signout
# Content-Type: {{contentType}} 
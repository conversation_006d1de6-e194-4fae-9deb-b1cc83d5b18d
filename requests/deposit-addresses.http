### Variables
@baseUrl = http://localhost:3001
@contentType = application/json

### Authentication (copy from auth.http)
@sessionToken = XXXXXXX
@sessionData = XXXXXXXXX

### Create Deposit Address for Project
POST {{baseUrl}}/deposit-addresses
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "projectId": "f04d8d0b-dc21-4beb-a849-14f6c444250d"
}

### Get All Deposit Addresses (Public endpoint - no auth required)
GET {{baseUrl}}/deposit-addresses

### Get Deposit Addresses by Project ID (Authenticated - only your own projects)
GET {{baseUrl}}/deposit-addresses/project/replace-with-actual-project-id
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

### Test with Different Project ID (to test authorization)
GET {{baseUrl}}/deposit-addresses/project/another-project-id
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

###
### NOTES:
### - Only 3 endpoints are available: POST /deposit-addresses, GET /deposit-addresses, GET /deposit-addresses/project/:projectId
### - CreateDepositAddressDto only requires: { "projectId": "string" }
### - Users can only create/view deposit addresses for projects they own
### - Replace "replace-with-actual-project-id" with real project IDs from your projects
### - Payment status updates are not exposed via API endpoints (service method exists but no controller endpoint)
### 
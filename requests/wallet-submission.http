### Wallet Submission Endpoint Tests

@baseUrl = https://api.testnet.caishen.io
@projectId = your-project-id-here

### Submit wallet address for approved project (protected endpoint)
POST {{baseUrl}}/projects/{{projectId}}/submit-wallet
Content-Type: application/json
<PERSON>ie: {{sessionCookie}}

{
  "walletAddress": "******************************************"
}

### Submit wallet address for approved project - Solana address
POST {{baseUrl}}/projects/{{projectId}}/submit-wallet  
Content-Type: application/json
Cookie: {{sessionCookie}}

{
  "walletAddress": "DRiP2Pn2K6fuMLKQmt5rZWxa91wMobTEuQ7sPTtfGMWE"
}

### Test with invalid wallet address (should fail validation)
POST {{baseUrl}}/public/projects/{{projectId}}/submit-wallet
Content-Type: application/json

{
  "walletAddress": "invalid-wallet-address"
}

### Test with non-existent project (should return 404)
POST {{baseUrl}}/public/projects/non-existent-project-id/submit-wallet
Content-Type: application/json

{
  "walletAddress": "******************************************"
}

### Get approved project details (to verify status)
GET {{baseUrl}}/public/projects/{{projectId}} 
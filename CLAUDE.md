# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `pnpm run start:dev` - Start development server with watch mode
- `pnpm run build` - Build the application
- `pnpm run start:prod` - Start production server

### Quality Assurance  
- `pnpm run check` - Run all quality checks (type-check, lint:check, format:check)
- `pnpm run type-check` - TypeScript type checking
- `pnpm run lint` / `pnpm run lint:fix` - ESLint with auto-fix
- `pnpm run format` / `pnpm run format:check` - Prettier formatting

### Database Operations
- `pnpm run db:generate` - Generate Drizzle migrations from schema changes
- `pnpm run db:migrate` - Run pending migrations
- `pnpm run db:push` - Push schema changes directly to database
- `pnpm run db:studio` - Open Drizzle Studio for database management

## Project Architecture

### Tech Stack
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth with email/phone OTP
- **Blockchain**: Multi-chain support (Solana, Ethereum, Arbitrum, Base)
- **Validation**: Zod schemas with automatic Swagger generation
- **Logging**: Pino logger with pretty printing for development

### Key Directories
- `src/config/env.ts` - Environment configuration with Zod validation
- `src/libs/` - Shared libraries (database, crypto encryption)
- `src/modules/` - Feature modules following NestJS patterns
- `src/utils/` - Utility functions
- `docs/` - Architecture and deployment documentation

### Module Structure
Each module follows NestJS conventions:
```
module-name/
├── module-name.controller.ts
├── module-name.service.ts  
├── module-name.module.ts
├── dto/ (request/response DTOs)
├── schemas/ (Zod validation schemas)
├── storage/ (database operations)
└── types/ (TypeScript interfaces)
```

## Core Features

### Multi-chain Blockchain Integration
- **Supported Chains**: Solana, Ethereum, Arbitrum, Base
- **Operations**: Wallet management, token transfers, liquidity pools
- **Security**: AES-256-GCM encryption for private keys

### External Integrations
- **Payment Providers**: MoonPay, YellowCard, CashRamp, OnrampMoney
- **Market Data**: CoinMarketCap, CoinGecko, LunarCrush, CoinDesk
- **Communications**: Twilio (SMS), Firebase (Push), Telegram

### Background Processing
- **Data Updaters**: Scheduled market data synchronization
- **Event System**: NestJS EventEmitter for decoupled communication
- **Rate Limiting**: Abstract rate-limited client for external APIs

## Development Guidelines

### Code Patterns
- Use Zod schemas for validation with automatic OpenAPI generation
- Follow NestJS dependency injection patterns
- Implement proper error handling with structured logging
- Use the crypto service for sensitive data encryption

### Database Operations
- Generate migrations after schema changes: `pnpm run db:generate`
- Always review generated migrations before applying
- Use Drizzle Studio for database inspection: `pnpm run db:studio`

### Environment Configuration
- All configuration is validated through `src/config/env.ts`
- Extensive external service API keys required (see env.ts for full list)
- Separate configurations for dev/testnet/production environments

## Important Files
- `docs/CRYPTO_SETUP.md` - Detailed encryption key setup guide
- `docs/CI_CD.md` - Comprehensive deployment pipeline documentation
- `requests/` - Example HTTP requests for API testing
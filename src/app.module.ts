import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { ScheduleModule } from '@nestjs/schedule'
import { LoggerModule } from 'nestjs-pino'
import { ZodSerializerInterceptor, ZodValidationPipe } from 'nestjs-zod'

import { getEnvConfig } from '@/config/env'
import { DatabaseModule } from '@/libs/database/database.module'
import { AuthModule } from '@/modules/auth/auth.module'
import { ChainsModule } from '@/modules/chains'
import { DataUpdatersModule } from '@/modules/data-updaters'
import { DepositAddressesModule } from '@/modules/deposit-addresses/deposit-addresses.module'
import { DiscoverModule } from '@/modules/discover'
import { EarnModule } from '@/modules/earn'
import { HistoryModule } from '@/modules/history'
import { NotificationsModule } from '@/modules/notifications'
import { PoolModule } from '@/modules/pool/pool.module'
import { ProjectsModule } from '@/modules/projects/projects.module'
import { RampModule } from '@/modules/ramp'
import { SweepModule } from '@/modules/sweep/sweep.module'
import { TelegramModule } from '@/modules/telegram'
import { TokensModule } from '@/modules/tokens'
import { UploadModule } from '@/modules/upload/upload.module'

import { AppController } from './app.controller'

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    ConfigModule.forRoot(),
    LoggerModule.forRoot({
      pinoHttp: {
        transport: getEnvConfig().env !== 'prod' ? { target: 'pino-pretty', options: { singleLine: true } } : undefined,
      },
    }),
    DatabaseModule,
    AuthModule,
    TokensModule,
    ChainsModule,
    HistoryModule,
    DiscoverModule,
    EarnModule,
    RampModule,
    NotificationsModule,
    DataUpdatersModule,
    TelegramModule,
    PoolModule,
    ProjectsModule,
    UploadModule,
    DepositAddressesModule,
    SweepModule,
  ],
  controllers: [AppController],
  providers: [
    { provide: APP_PIPE, useClass: ZodValidationPipe },
    { provide: APP_INTERCEPTOR, useClass: ZodSerializerInterceptor },
  ],
})
export class AppModule {}

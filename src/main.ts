import { ValidationPipe, VersioningType } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerDocumentOptions, SwaggerModule } from '@nestjs/swagger'
import { toNode<PERSON>and<PERSON> } from 'better-auth/node'
import { Request, Response, NextFunction } from 'express'
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino'
import { patchNestJsSwagger } from 'nestjs-zod'

import { AppModule } from './app.module'
import { envConfigSchema, getEnvConfig } from './config/env'
import { AuthService } from './modules/auth/auth.service'

async function bootstrap() {
  const config = getEnvConfig()
  const configParseResult = envConfigSchema.safeParse(config)

  if (!configParseResult.success) {
    throw new Error(`Invalid environment config: ${JSON.stringify(configParseResult.error.issues)}`)
  }

  // Debug logging for environment detection
  // eslint-disable-next-line no-console
  console.log('Environment detection:', {
    NODE_ENV: process.env.NODE_ENV,
    configEnv: config.env,
    isProduction: process.env.NODE_ENV === 'production',
  })

  // Configure CORS to allow all origins for testnet
  const corsOptions = {
    origin: true, // Allow all origins for testnet
    credentials: true,
  }

  // eslint-disable-next-line no-console
  console.log('CORS configuration:', corsOptions)

  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    cors: corsOptions,
  })

  // IMPORTANT: Add CORS middleware FIRST, before Better Auth middleware
  app.use((req: Request, res: Response, next: NextFunction) => {
    const { origin } = req.headers

    // When credentials are included, we can't use '*' - must use specific origin
    if (origin) {
      res.header('Access-Control-Allow-Origin', origin)
    } else {
      res.header('Access-Control-Allow-Origin', '*')
    }

    res.header('Access-Control-Allow-Credentials', 'true')
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    res.header(
      'Access-Control-Allow-Headers',
      'Origin,X-Requested-With,Content-Type,Accept,Authorization,x-xsrf-token,X-Admin-Key',
    )

    if (req.method === 'OPTIONS') {
      res.sendStatus(200)

      return
    }

    next()
  })

  // Add Better Auth middleware AFTER CORS middleware
  app.use((req: Request, res: Response, next: NextFunction) => {
    // eslint-disable-next-line no-console
    console.log(`Middleware: ${req.method} ${req.path}`)

    // Only route specific Better Auth endpoints, not all /auth/* paths
    const betterAuthPaths = [
      '/auth/email-otp/',
      '/auth/sign-in/',
      '/auth/sign-up/',
      '/auth/sign-out',
      '/auth/callback/',
      '/auth/error',
    ]

    const shouldUseBetterAuth = betterAuthPaths.some((path) => req.path.startsWith(path))

    if (shouldUseBetterAuth) {
      // eslint-disable-next-line no-console
      console.log('Handling with Better Auth')

      // Get the AuthService lazily (after init has been called)
      const authService = app.get(AuthService)
      const betterAuthHandler = toNodeHandler(authService.auth)

      return betterAuthHandler(req, res)
    }

    // eslint-disable-next-line no-console
    console.log('Passing to NestJS')

    return next()
  })

  app.useLogger(app.get(Logger))
  app.useGlobalInterceptors(new LoggerErrorInterceptor())

  app.useGlobalPipes(new ValidationPipe({ forbidNonWhitelisted: true, transform: true }))
  app.enableVersioning({ type: VersioningType.URI })

  patchNestJsSwagger()

  const swaggerConfig = new DocumentBuilder()
    .setTitle('Inclusive Layer Token API')
    .setVersion('1.0')
    .setDescription('Authentication API with Email OTP and Phone OTP verification')
    .addCookieAuth('caishen-pro.session_token', {
      type: 'http',
      in: 'cookie',
      name: 'caishen-pro.session_token',
      description: 'Session token for authentication',
    })
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      description: 'Session token',
    })
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-Admin-Key',
        in: 'header',
        description: 'Admin access key for administrative operations',
      },
      'AdminKey',
    )
    .build()
  const options: SwaggerDocumentOptions = { deepScanRoutes: true }
  const document = SwaggerModule.createDocument(app, swaggerConfig, options)
  SwaggerModule.setup('swagger', app, document, {
    swaggerOptions: { syntaxHighlight: { activated: false } },
  })

  // Initialize the app to ensure all lifecycle hooks are called
  await app.init()

  await app.listen(getEnvConfig().port)
}

void bootstrap()

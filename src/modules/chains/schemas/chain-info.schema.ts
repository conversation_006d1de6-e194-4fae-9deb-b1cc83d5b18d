import { z } from 'zod'

export const chainInfoSchema = z.object({
  id: z.string(),
  chainId: z.number(),
  ticker: z.string(),
  fullName: z.string(),
  imageUrl: z.string(),
  addressRegex: z.string(),
  chainNamespace: z.enum(['eip155', 'solana']),
  nativeToken: z.object({
    name: z.string(),
    symbol: z.string(),
    decimals: z.number(),
    coincapId: z.string().optional(),
    coincompareId: z.string().optional(),
    tokenId: z.string(),
    address: z.string(),
  }),
  rpcUrls: z.array(z.string()),
  blockExplorerUrls: z.array(z.string()),
  coincompareId: z.string(),
  transakId: z.string().optional(),
})

export type ChainInfo = z.infer<typeof chainInfoSchema>

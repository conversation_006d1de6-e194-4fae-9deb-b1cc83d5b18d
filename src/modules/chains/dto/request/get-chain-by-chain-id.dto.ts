import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsNumber } from 'class-validator'

export class GetChainByChainIdDto {
  @ApiProperty({ name: 'id', description: 'Chain ID', type: Number, example: 1 })
  @IsNumber()
  @Transform(({ value }) => {
    const number = Number(value)

    if (!Number.isNaN(number)) return number

    return value
  })
  id: number
}

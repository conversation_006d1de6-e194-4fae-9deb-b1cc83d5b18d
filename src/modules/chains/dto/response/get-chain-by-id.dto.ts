import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean } from 'class-validator'

import { ChainInfoDto } from './chain-info.dto'

export class GetChainByIdResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({
    description: 'Chain info',
    example: {
      blockExplorerUrls: ['https://arbiscan.io/tx/PARAM_TX_HASH'],
      fullName: 'Arbitrum',
      transakId: 'arbitrum',
      ticker: 'arbitrum',
      chainNamespace: 'eip155',
      imageUrl: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11841.png',
      addressRegex: '^(0x)[0-9A-Fa-f]{40}$',
      chainId: 42161,
      nativeCurrency: {
        decimals: 18,
        tokenId: 'PgXn7bKa1FmYUvceATHY',
        coincapId: 'ethereum',
        symbol: 'ETH',
        address: '******************************************',
        name: '<PERSON><PERSON>',
        coincompareId: 'ETH',
      },
      coincompareId: 'ARB',
      rpcUrls: ['https://ankr.com/arbitrum'],
      id: 'arbitrum',
    },
    required: true,
  })
  @Type(() => ChainInfoDto)
  data: ChainInfoDto
}

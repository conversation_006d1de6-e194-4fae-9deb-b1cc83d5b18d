import { Injectable } from '@nestjs/common'

import { type ChainInfo } from './schemas/chain-info.schema'
import { ChainsStorage } from './storage'

@Injectable()
export class ChainsService {
  constructor(private storage: ChainsStorage) {}

  async getChains(): Promise<ChainInfo[]> {
    return this.storage.getChains()
  }

  async getChainById(id: string): Promise<ChainInfo | undefined> {
    return this.storage.getChainById(id)
  }

  async getChainByChainId(id: number): Promise<ChainInfo | undefined> {
    return this.storage.getChainByChainId(id)
  }

  /**
   * Get numeric chain ID for EVM chains by chain name
   * @param chainName - Chain name (e.g., 'mainnet', 'arbitrum', 'base')
   * @returns Promise<number> - The numeric chain ID
   * @throws Error if chain is not found or not an EVM chain
   */
  async getEvmChainId(chainName: string): Promise<number> {
    const chainInfo = await this.getChainById(chainName)

    if (!chainInfo) {
      throw new Error(`Chain not found: ${chainName}`)
    }

    if (chainInfo.chainNamespace !== 'eip155') {
      throw new Error(`Chain ${chainName} is not an EVM chain`)
    }

    return chainInfo.chainId
  }
}

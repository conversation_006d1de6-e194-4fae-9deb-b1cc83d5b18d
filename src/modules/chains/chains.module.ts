import { Module } from '@nestjs/common'

import { ChainsController } from './chains.controller'
import { ChainsService } from './chains.service'
import { ChainsStorage } from './storage'
import { FirebaseModule } from '../firebase'

@Module({
  imports: [FirebaseModule],
  providers: [ChainsStorage, ChainsService],
  controllers: [ChainsController],
  exports: [ChainsService],
})
export class ChainsModule {}

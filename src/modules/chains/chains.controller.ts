import { <PERSON>, Get, HttpException, Logger, Param } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { ChainsService } from './chains.service'
import { GetChainByIdDto } from './dto/request/get-chain-by-id.dto'
import { GetChainByIdResponseDto } from './dto/response/get-chain-by-id.dto'
import { GetChainsResponseDto } from './dto/response/get-chains.dto'

@ApiTags('Chains')
@Controller({
  path: '/chains',
  version: '1',
})
export class ChainsController {
  private logger = new Logger('ChainsController')

  constructor(private readonly chainsService: ChainsService) {}

  @ApiOperation({ description: 'Get all chains data' })
  @ApiOkResponse({ type: GetChainsResponseDto })
  @Get('/')
  async getChainsList(): Promise<GetChainsResponseDto> {
    this.logger.log(`Get all chains data`)
    const chains = await this.chainsService.getChains()

    return { success: true, total: chains.length, data: chains }
  }

  @ApiOperation({ description: 'Get chain data by id' })
  @ApiOkResponse({ type: GetChainByIdResponseDto })
  @Get(':id')
  async getChainById(@Param() params: GetChainByIdDto): Promise<GetChainByIdResponseDto> {
    const { id } = params
    this.logger.log(`Get chain by id: ${id}`)
    const chain = await this.chainsService.getChainById(id)

    if (!chain) {
      throw new HttpException({ success: false }, 404)
    }

    return { success: true, data: chain }
  }

  // @ApiOperation({ description: 'Get chain data by chain id' })
  // @ApiOkResponse({ type: GetChainByIdResponseDto })
  // @Get('/chainId/:id')
  // async getChainByChainId(@Param() params?: GetChainByChainIdDto): Promise<GetChainByIdResponseDto> {
  //   const { id } = params
  //   this.logger.log(`Get chain by chainId: ${id}`)
  //   const chain = await this.chainsService.getChainByChainId(id)
  //
  //   if (!chain) {
  //     throw new HttpException({ success: false }, 404)
  //   }
  //
  //   return { success: true, data: chain }
  // }
}

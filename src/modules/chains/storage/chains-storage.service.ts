import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

import { Storage } from '@/abstract/storage'
import { getErrorMessage } from '@/utils/get-error-message'

import { type ChainInfoFromStorage, chainInfoFromStorageSchema } from './chains-storage.schema'
import { FirebaseService } from '../../firebase'
import { type ChainInfo, chainInfoSchema } from '../schemas/chain-info.schema'

@Injectable()
export class ChainsStorage extends Storage implements OnModuleInit {
  private logger = new Logger('ChainsStorage')

  private chains: ChainInfo[] = []

  constructor(private firebase: FirebaseService) {
    super()
  }

  onModuleInit() {
    void this.fetchChains().catch((error) =>
      this.logger.error(`Error on init ChainsStorage ${getErrorMessage(error)}`, error),
    )
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    waitForCompletion: true,
    name: 'update<PERSON>hai<PERSON>',
  })
  async updateChains(): Promise<void> {
    await this.fetchChains()
  }

  async getChains(): Promise<ChainInfo[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    const result = this.chains
    result.forEach((chain) => chainInfoSchema.parse(chain))

    return result
  }

  async getChainById(id: string): Promise<ChainInfo | undefined> {
    if (!this.isInitialized) await this.waitForInitPromise

    const chain = this.chains.find((t) => t.id === id)

    if (chain) {
      chainInfoSchema.parse(chain)

      return chain
    }

    return undefined
  }

  async getChainByChainId(id: number): Promise<ChainInfo | undefined> {
    if (!this.isInitialized) await this.waitForInitPromise

    const chain = this.chains.find((t) => t.chainId === id)

    if (chain) {
      chainInfoSchema.parse(chain)

      return chain
    }

    return undefined
  }

  private async fetchChains(): Promise<void> {
    try {
      const chainsFromStorage = await this.firebase.getAllDocuments<ChainInfoFromStorage>('chains')

      if (!chainsFromStorage || !chainsFromStorage.length) throw new Error('Chains not found')

      const chains = chainsFromStorage.reduce((acc, chain) => {
        const res = chainInfoFromStorageSchema.safeParse(chain)

        if (res.success) {
          const { nativeCurrency, ...chainWithoutNativeCurrency } = chain
          const mapped: ChainInfo = {
            ...chainWithoutNativeCurrency,
            nativeToken: chain.nativeCurrency,
          }

          return [...acc, mapped]
        }

        this.logger.log(`Cannot parse chain info for id ${chain.id}`)

        return acc
      }, [] as ChainInfo[])

      this.logger.log('Fetched chains from firebase')
      this.chains = chains
      this.setIsInitialized()
    } catch (error) {
      this.logger.error(`Failed to fetch chains from firebase: ${getErrorMessage(error)}`)
    }
  }
}

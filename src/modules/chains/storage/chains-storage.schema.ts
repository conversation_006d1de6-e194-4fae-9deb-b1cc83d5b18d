import { z } from 'zod'

export const chainInfoFromStorageSchema = z.object({
  id: z.string(),
  chainId: z.number(),
  ticker: z.string(),
  fullName: z.string(),
  imageUrl: z.string(),
  addressRegex: z.string(),
  chainNamespace: z.enum(['eip155', 'solana']),
  nativeCurrency: z.object({
    name: z.string(),
    symbol: z.string(),
    decimals: z.number(),
    coincapId: z.string().optional(),
    coincompareId: z.string().optional(),
    tokenId: z.string(),
    address: z.string(),
  }),
  rpcUrls: z.array(z.string()),
  blockExplorerUrls: z.array(z.string()),
  coincompareId: z.string(),
  transakId: z.string().optional(),
})

export type ChainInfoFromStorage = z.infer<typeof chainInfoFromStorageSchema>

import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Bo<PERSON> } from 'grammy'
import type { ParseMode } from 'grammy/types'

import { getErrorMessage } from '@/utils/get-error-message'

import { getEnvConfig } from '../../config/env'

@Injectable()
export class TelegramService implements OnModuleInit {
  private logger = new Logger('TelegramService')

  private bot: Bot

  async onModuleInit() {
    try {
      const { telegram } = getEnvConfig()
      this.bot = new Bot(telegram.apiToken)
      this.bot.catch((e) => {
        this.logger.error(e)
      })

      void this.bot.start()

      this.logger.log('TelegramService initialized')
    } catch (error) {
      this.logger.error(`Error on init TelegramService ${getErrorMessage(error)}`, error)

      throw error
    }
  }

  sendMessage(message: string, parseMode?: ParseMode): void {
    const { telegram } = getEnvConfig()

    void this.bot.api.sendMessage(telegram.chatId, message, { parse_mode: parseMode })
  }
}

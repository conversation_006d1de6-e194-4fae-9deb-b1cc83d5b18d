import { Controller, Get, Logger } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { DiscoverService } from './discover.service'
import { GetSectionsResponseDto } from './dto/response/get-sections.dto'

@ApiTags('Discover')
@Controller({
  path: '/discover',
  version: '1',
})
export class DiscoverController {
  private logger = new Logger('DiscoverController')

  constructor(private readonly discoverService: DiscoverService) {}

  @ApiOperation({ description: 'Get discover sections data' })
  @ApiOkResponse({ type: GetSectionsResponseDto })
  @Get('/sections')
  async getDiscoverSections(): Promise<GetSectionsResponseDto> {
    this.logger.log(`Get discover sections`)
    const sections = await this.discoverService.getDiscoverSections()

    return { success: true, data: sections }
  }
}

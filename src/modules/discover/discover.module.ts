import { Module } from '@nestjs/common'

import { DiscoverController } from './discover.controller'
import { DiscoverService } from './discover.service'
import { DiscoverStorage } from './storage'
import { FirebaseModule } from '../firebase'

@Module({
  exports: [DiscoverService],
  imports: [FirebaseModule],
  providers: [DiscoverStorage, DiscoverService],
  controllers: [DiscoverController],
})
export class DiscoverModule {}

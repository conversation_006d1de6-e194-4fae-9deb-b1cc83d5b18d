import { z } from 'zod'

export const highlightSchema = z.object({
  id: z.string(),
  type: z.literal('twitter'),
  headline: z.string(),
  source: z.string(),
  time: z.number(),
  url: z.string(),
})

export const tokenInfoSchema = z.object({
  id: z.string(),
  tags: z.array(z.string()),
})

export const categorySchema = z.object({
  name: z.string(),
  color: z.string(),
  bgColor: z.string(),
  icon: z.string().optional().nullable(),
})

export const filtersSchema = z.object({
  categories: z.array(categorySchema),
})

export const newsSchema = z.object({
  id: z.string(),
  headline: z.string(),
  source: z.string(),
  sourceImageUrl: z.string(),
  sourceUrl: z.string(),
  time: z.number(),
  categories: z.array(z.string()),
})

export const tokensListSectionSchema = z.object({
  id: z.string(),
  type: z.enum(['tokensListVertical', 'tokensListHorizontal'] as const),
  title: z.string(),
  visibleCount: z.number(),
  filters: filtersSchema,
  tokens: z.array(tokenInfoSchema),
  subtitle: z.string().nullable(),
  icon: z.string().nullable(),
})

export const socialListSectionSchema = z.object({
  id: z.string(),
  type: z.literal('twitter'),
  title: z.string(),
  highlights: z.array(highlightSchema),
})

export const newsListSectionSchema = z.object({
  id: z.string(),
  type: z.literal('newsList'),
  title: z.string(),
  filters: filtersSchema,
  news: z.array(newsSchema),
  visibleCount: z.number(),
})

export const sectionSchema = z.discriminatedUnion('type', [
  tokensListSectionSchema,
  socialListSectionSchema,
  newsListSectionSchema,
])

export type Section = z.infer<typeof sectionSchema>
export type SocialListSection = z.infer<typeof socialListSectionSchema>
export type NewsListSection = z.infer<typeof newsListSectionSchema>
export type TokensListSection = z.infer<typeof tokensListSectionSchema>

export type Highlight = z.infer<typeof highlightSchema>
export type News = z.infer<typeof newsSchema>

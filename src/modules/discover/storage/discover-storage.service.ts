import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import { nanoid } from 'nanoid'

import { Storage } from '@/abstract/storage'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  discoverConfigSchema,
  type DiscoverConfig,
  type SectionFromStorage,
  sectionFromStorageSchema,
} from './discover-storage.schema'
import { FirebaseService } from '../../firebase'
import { type Section, sectionSchema } from '../schemas/section.schema'

@Injectable()
export class DiscoverStorage extends Storage implements OnModuleInit {
  private logger = new Logger('DiscoverStorage')

  sections: Section[] = []

  constructor(private firebase: FirebaseService) {
    super()
  }

  onModuleInit() {
    void this.fetchDiscoverSections()
  }

  async revalidateCache(): Promise<void> {
    if (!this.isInitialized) return

    super.revalidateCache()
    this.sections = []
    this.onModuleInit()
  }

  @Cron(CronExpression.EVERY_MINUTE, {
    waitForCompletion: true,
    name: 'updateDiscoverSections',
  })
  private async updateDiscoverSections(): Promise<void> {
    if (!this.isInitialized && !this.isInitializing) {
      this.logger.warn(`DiscoverStorage isn't initialized, initializing...`)
      await this.fetchDiscoverSections()
    }
  }

  async getDiscoverSections(): Promise<Section[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    return this.sections
  }

  async setDiscoverSection(id: string, section: Section, skipRevalidation?: boolean): Promise<void> {
    const sectionToParse =
      section.type === 'tokensListVertical' || section.type === 'tokensListHorizontal'
        ? { ...section, subTitle: section.subtitle }
        : section
    const sectionToSave = sectionFromStorageSchema.parse(sectionToParse)
    await this.firebase.saveDocument('discoverSections', sectionToSave, id)

    if (!skipRevalidation) {
      await this.revalidateCache()
    }
  }

  private async fetchDiscoverSections(): Promise<void> {
    try {
      const sectionsFromStorage = await this.firebase.getAllDocuments<SectionFromStorage>('discoverSections')
      const order = await this.fetchDiscoverSectionsOrder()

      if (!sectionsFromStorage) throw new Error('Unable to fetch discover sections')

      const sections = sectionsFromStorage.reduce(
        (acc, section) => {
          const isSection = sectionFromStorageSchema.safeParse(section)

          if (!isSection) {
            this.logger.error('Cannot parse discover section')

            return acc
          }

          return { ...acc, [section.id]: section }
        },
        {} as Record<string, SectionFromStorage>,
      )

      const mappedSections: Section[] = order
        .map((id) => sections[id])
        .filter((section) => !!section)
        .map((section) => {
          if (section.type === 'twitter') {
            return { ...section, highlights: section.highlights.map((highlight) => ({ ...highlight, id: nanoid() })) }
          }

          if (section.type === 'newsList') {
            return { ...section, news: section.news.map((news) => ({ ...news, id: nanoid() })) }
          }

          const { subTitle, ...rest } = section

          return { ...rest, subtitle: subTitle }
        })

      mappedSections.filter((section) => {
        const res = sectionSchema.safeParse(section)

        if (!res.success) this.logger.error(`Mapped section is incorrect: ${JSON.stringify(res.error.errors)}`)

        return res.success
      })

      this.sections = mappedSections
      this.setIsInitialized()
      this.logger.log('Fetched discover sections from firebase')
    } catch (e) {
      this.logger.error(`Failed to fetch discover sections from firebase: ${getErrorMessage(e)}`)
      this.isInitializing = false
    }
  }

  private async fetchDiscoverSectionsOrder(): Promise<string[]> {
    const order = await this.firebase.getDocument<DiscoverConfig>('discoverData', 'main')

    if (!order) throw new Error('Unable to fetch discover sections order')

    const result = discoverConfigSchema.safeParse(order)

    if (result.error) return []

    return order.sections
  }
}

import { z } from 'zod'

import {
  highlightSchema,
  newsSchema,
  tokensListSectionSchema,
  socialListSectionSchema,
  newsListSectionSchema,
} from '../schemas/section.schema'

export const discoverConfigSchema = z.object({
  id: z.string(),
  sections: z.array(z.string()),
  createdAt: z.string(),
  updatedAt: z.string(),
})

export type DiscoverConfig = z.infer<typeof discoverConfigSchema>

const highlightFromStorageSchema = highlightSchema.omit({ id: true })
const newsFromStorageSchema = newsSchema.omit({ id: true })

export const socialListSectionFromStorageSchema = socialListSectionSchema
  .omit({ highlights: true })
  .and(z.object({ highlights: z.array(highlightFromStorageSchema) }))

export const newsListSectionFromStorageSchema = newsListSectionSchema
  .omit({ news: true })
  .and(z.object({ news: z.array(newsFromStorageSchema) }))

export const tokensListSectionFromStorageSchema = tokensListSectionSchema
  .omit({ subtitle: true })
  .and(z.object({ subTitle: z.string().nullable() }))

export const sectionFromStorageSchema = tokensListSectionFromStorageSchema
  .or(socialListSectionFromStorageSchema)
  .or(newsListSectionFromStorageSchema)

export type SectionFromStorage = z.infer<typeof sectionFromStorageSchema>

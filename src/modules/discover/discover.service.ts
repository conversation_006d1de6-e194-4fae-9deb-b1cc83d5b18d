import { Injectable } from '@nestjs/common'

import type { Section } from './schemas/section.schema'
import { DiscoverStorage } from './storage'

@Injectable()
export class DiscoverService {
  constructor(private storage: DiscoverStorage) {}

  async getDiscoverSections(): Promise<Section[]> {
    const sections = await this.storage.getDiscoverSections()

    if (!sections.length) throw new Error('Discover sections were not found')

    return sections
  }

  async setDiscoverSection(id: string, section: Section, skipRevalidation?: boolean): Promise<void> {
    await this.storage.setDiscoverSection(id, section, skipRevalidation)
  }

  async revalidateCache(): Promise<void> {
    await this.storage.revalidateCache()
  }
}

import { createZodDto } from 'nestjs-zod'

import { tokensListSectionSchema, socialListSectionSchema, newsListSectionSchema } from '../../schemas/section.schema'

export class TokensListSectionDto extends createZodDto(tokensListSectionSchema) {}

export class SocialListSectionDto extends createZodDto(socialListSectionSchema) {}

export class NewsListSectionDto extends createZodDto(newsListSectionSchema) {}

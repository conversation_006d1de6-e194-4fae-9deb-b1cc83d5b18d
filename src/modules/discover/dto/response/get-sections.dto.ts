import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsBoolean, ValidateNested } from 'class-validator'

import { TokensListSectionDto, NewsListSectionDto, SocialListSectionDto } from '../common/section.dto'

export class GetSectionsResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({
    description: 'Array of discover sections',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TokensListSectionDto || NewsListSectionDto || SocialListSectionDto)
  data: (TokensListSectionDto | NewsListSectionDto | SocialListSectionDto)[]
}

import { Module, forwardRef } from '@nestjs/common'

import { IndexerModule } from '@/libs/indexer'

import { PoolController } from './pool.controller'
import { PoolService } from './pool.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { AuthModule } from '../auth/auth.module'
import { BlockchainModule } from '../blockchain/blockchain.module'
import { ChainsModule } from '../chains/chains.module'
// eslint-disable-next-line import/no-cycle
import { ProjectsModule } from '../projects/projects.module'

@Module({
  imports: [
    BlockchainModule,
    DatabaseModule,
    AuthModule,
    IndexerModule,
    ChainsModule,
    forwardRef(() => ProjectsModule),
  ],
  controllers: [PoolController],
  providers: [PoolService],
  exports: [PoolService],
})
export class PoolModule {}

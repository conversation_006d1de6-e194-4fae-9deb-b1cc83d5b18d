import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty } from 'class-validator'

export class CheckLiquidityDto {
  @ApiProperty({
    description: 'Project ID to check liquidity for',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsString()
  @IsNotEmpty()
  projectId: string
}

export class CheckLiquidityResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean

  @ApiProperty({
    description: 'Whether liquidity has been added by the manager',
    example: true,
  })
  hasLiquidity: boolean

  @ApiProperty({
    description: 'Pool address if found',
    example: '******************************************',
    required: false,
  })
  poolAddress?: string

  @ApiProperty({
    description: 'Project manager address',
    example: '******************************************',
    required: false,
  })
  projectManager?: string

  @ApiProperty({
    description: 'Current project status',
    example: 'liquidity_added',
    required: false,
  })
  projectStatus?: string

  @ApiProperty({
    description: 'Error message if operation failed',
    example: 'Project not found',
    required: false,
  })
  error?: string
}

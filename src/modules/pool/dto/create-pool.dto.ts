import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsN<PERSON>ber, IsUUID, IsOptional } from 'class-validator'

export class CreatePoolDto {
  @ApiProperty({
    description: 'Project ID - all pool configuration will be auto-populated from project data',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  projectId: string

  @ApiPropertyOptional({
    description: 'Token launch type. Defaults to 0 if not provided.',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  tokenLaunchType?: number

  @ApiPropertyOptional({
    description: 'Whitelisted address for swap. Defaults to zero address if not provided.',
    example: '0x9Fbf3Fc7a1d2F4065A804CAba50Fa8F510f0340D',
  })
  @IsOptional()
  @IsString()
  whitelistedAddressForSwap?: string

  @ApiPropertyOptional({
    description: 'Project manager address. Defaults to zero address if not provided.',
    example: '0x5A22fA9238b53722486C33Ed5ab92a1AB9e86718',
  })
  @IsOptional()
  @IsString()
  projectManager?: string
}

import { Injectable, Inject, Logger, NotFoundException, ConflictException } from '@nestjs/common'
import { Keypair } from '@solana/web3.js'
import { eq, and, type InferInsertModel } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'
import { generatePrivateKey, privateKeyToAccount } from 'viem/accounts'

import { CryptoService } from '@/libs/crypto/crypto.service'
import * as schema from '@/libs/database/schema'
import { projects } from '@/libs/database/schema'
import { depositAddresses, type NewDepositAddress } from '@/libs/database/schema/deposit-addresses.schema'

import { CreateDepositAddressDto } from './dto/request/create-deposit-address.dto'

type DepositAddressUpdate = Partial<InferInsertModel<typeof depositAddresses>>

export interface DepositAddressResult {
  evm: {
    publicAddress: string
  }
  solana: {
    publicAddress: string
  }
  record: {
    id: string
    projectId: string
    paymentStatus: string
    createdAt: Date
  }
}

@Injectable()
export class DepositAddressesService {
  private readonly logger = new Logger(DepositAddressesService.name)

  constructor(
    @Inject('DB') private readonly db: NodePgDatabase<typeof schema>,
    private readonly cryptoService: CryptoService,
  ) {}

  async createDepositAddress(dto: CreateDepositAddressDto, userId: string): Promise<DepositAddressResult> {
    this.logger.log(`Creating deposit addresses for project: ${dto.projectId} by user: ${userId}`)

    // Validate that the user owns the project
    await this.validateProjectOwnership(dto.projectId, userId)

    // Check if deposit address already exists for this project
    const existingAddress = await this.db.query.depositAddresses.findFirst({
      where: eq(depositAddresses.projectId, dto.projectId),
    })

    if (existingAddress) {
      throw new ConflictException('Deposit address already exists for this project')
    }

    // Generate both EVM and Solana keypairs
    const [evmKeyPair, solanaKeyPair] = await Promise.all([this.generateEvmKeyPair(), this.generateSolanaKeyPair()])

    const newDepositAddress: NewDepositAddress = {
      projectId: dto.projectId,
      evmPublicAddress: evmKeyPair.publicAddress,
      evmEncryptedPrivateKey: evmKeyPair.encryptedPrivateKey,
      solanaPublicAddress: solanaKeyPair.publicAddress,
      solanaEncryptedPrivateKey: solanaKeyPair.encryptedPrivateKey,
    }

    const [result] = await this.db.insert(depositAddresses).values(newDepositAddress).returning()

    this.logger.log(
      `Successfully created deposit addresses - EVM: ${result.evmPublicAddress}, Solana: ${result.solanaPublicAddress}`,
    )

    return {
      evm: {
        publicAddress: evmKeyPair.publicAddress,
      },
      solana: {
        publicAddress: solanaKeyPair.publicAddress,
      },
      record: {
        id: result.id,
        projectId: result.projectId,
        paymentStatus: result.paymentStatus,
        createdAt: result.createdAt,
      },
    }
  }

  private async generateEvmKeyPair() {
    // Generate a random private key for EVM
    const privateKey = generatePrivateKey()
    const account = privateKeyToAccount(privateKey)

    return {
      publicAddress: account.address,
      encryptedPrivateKey: await this.encryptPrivateKey(privateKey),
    }
  }

  private async generateSolanaKeyPair() {
    // Generate a random keypair for Solana
    const keypair = Keypair.generate()
    const privateKeyBytes = keypair.secretKey

    return {
      publicAddress: keypair.publicKey.toBase58(),
      encryptedPrivateKey: await this.encryptPrivateKey(Buffer.from(privateKeyBytes).toString('hex')),
    }
  }

  private async encryptPrivateKey(privateKey: string): Promise<string> {
    return this.cryptoService.encryptPrivateKey(privateKey)
  }

  async findByProjectId(projectId: string, userId: string) {
    // Validate that the user owns the project
    await this.validateProjectOwnership(projectId, userId)

    return this.db.query.depositAddresses.findFirst({
      where: eq(depositAddresses.projectId, projectId),
    })
  }

  async findAll() {
    return this.db.query.depositAddresses.findMany({
      orderBy: (addresses, { desc }) => [desc(addresses.createdAt)],
    })
  }

  async updatePaymentStatus(id: string, status: 'pending' | 'swept', amountReceived?: string) {
    const updates: DepositAddressUpdate = { paymentStatus: status }

    if (amountReceived) {
      updates.amountReceived = amountReceived
    }

    return this.db.update(depositAddresses).set(updates).where(eq(depositAddresses.id, id)).returning()
  }

  private async validateProjectOwnership(projectId: string, userId: string): Promise<void> {
    const project = await this.db
      .select()
      .from(projects)
      .where(and(eq(projects.projectId, projectId), eq(projects.userId, userId)))
      .limit(1)

    if (!project.length) {
      throw new NotFoundException('Project not found or access denied')
    }

    this.logger.log(`Validated project ownership: ${projectId} belongs to user: ${userId}`)
  }
}

import { Module } from '@nestjs/common'

import { CryptoModule } from '@/libs/crypto/crypto.module'
import { DatabaseModule } from '@/libs/database/database.module'

import { DepositAddressesController } from './deposit-addresses.controller'
import { DepositAddressesService } from './deposit-addresses.service'
import { AuthModule } from '../auth/auth.module'

@Module({
  imports: [DatabaseModule, CryptoModule, AuthModule],
  controllers: [DepositAddressesController],
  providers: [DepositAddressesService],
  exports: [DepositAddressesService],
})
export class DepositAddressesModule {}

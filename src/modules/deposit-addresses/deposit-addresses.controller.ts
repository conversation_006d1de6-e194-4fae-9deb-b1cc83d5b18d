import { Controller, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common'
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiCookieAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger'

import { DepositAddressesService } from './deposit-addresses.service'
import { CreateDepositAddressDto } from './dto/request/create-deposit-address.dto'
import { CookieAuthGuard } from '../auth/guards/cookie-auth.guard'

@ApiTags('Deposit Addresses')
@Controller('deposit-addresses')
export class DepositAddressesController {
  constructor(private readonly depositAddressesService: DepositAddressesService) {}

  @Post()
  @UseGuards(CookieAuthGuard)
  @ApiCookieAuth('better-auth.session_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create Deposit Address',
    description:
      'Create a new deposit address for a project. Requires authentication. Users can only create deposit addresses for their own projects.',
  })
  @ApiResponse({
    status: 201,
    description: 'Deposit address created successfully',
    schema: {
      example: {
        evm: {
          publicAddress: '0x1234567890abcdef...',
        },
        solana: {
          publicAddress: 'So1ana1234567890abcdef...',
        },
        record: {
          id: 'addr_123',
          projectId: 'proj_456',
          paymentStatus: 'pending',
          createdAt: '2025-01-23T10:00:00Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
    schema: {
      example: {
        error: 'Bad Request',
        message: ['projectId should not be empty'],
        statusCode: 400,
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      example: {
        error: 'Unauthorized',
        message: 'Authentication required',
        statusCode: 401,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found or access denied',
    schema: {
      example: {
        error: 'Not Found',
        message: 'Project not found or access denied',
        statusCode: 404,
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Deposit address already exists for this project',
    schema: {
      example: {
        error: 'Conflict',
        message: 'Deposit address already exists for this project',
        statusCode: 409,
      },
    },
  })
  async createDepositAddress(@Request() req: any, @Body() createDepositAddressDto: CreateDepositAddressDto) {
    const userId = req.user.id

    return this.depositAddressesService.createDepositAddress(createDepositAddressDto, userId)
  }

  @Get()
  @ApiOperation({
    summary: 'Get All Deposit Addresses',
    description: 'Retrieve all deposit addresses in the system.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of all deposit addresses retrieved successfully',
    schema: {
      example: [
        {
          id: 'addr_123',
          projectId: 'proj_456',
          projectName: 'My Project',
          launchType: 'presale',
          publicAddress: '0x1234567890abcdef...',
          network: 'ethereum',
          status: 'active',
          createdAt: '2025-01-23T10:00:00Z',
          updatedAt: '2025-01-23T10:00:00Z',
        },
        {
          id: 'addr_789',
          projectId: 'proj_101',
          projectName: 'Another Project',
          launchType: 'launch',
          publicAddress: '0xfedcba0987654321...',
          network: 'ethereum',
          status: 'active',
          createdAt: '2025-01-23T09:00:00Z',
          updatedAt: '2025-01-23T09:00:00Z',
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      example: {
        error: 'Internal Server Error',
        message: 'Failed to retrieve deposit addresses',
        statusCode: 500,
      },
    },
  })
  async findAll() {
    return this.depositAddressesService.findAll()
  }

  @Get('project/:projectId')
  @UseGuards(CookieAuthGuard)
  @ApiCookieAuth('better-auth.session_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get Deposit Addresses by Project ID',
    description:
      'Retrieve all deposit addresses associated with a specific project. Users can only access their own projects.',
  })
  @ApiParam({
    name: 'projectId',
    description: 'The unique identifier of the project',
    example: 'proj_456',
  })
  @ApiResponse({
    status: 200,
    description: 'Deposit addresses for the project retrieved successfully',
    schema: {
      example: [
        {
          id: 'addr_123',
          projectId: 'proj_456',
          projectName: 'My Project',
          launchType: 'presale',
          publicAddress: '0x1234567890abcdef...',
          network: 'ethereum',
          status: 'active',
          createdAt: '2025-01-23T10:00:00Z',
          updatedAt: '2025-01-23T10:00:00Z',
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No deposit addresses found for the specified project or project not found',
    schema: {
      example: {
        error: 'Not Found',
        message: 'Project not found or access denied',
        statusCode: 404,
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid project ID format',
    schema: {
      example: {
        error: 'Bad Request',
        message: 'Invalid project ID format',
        statusCode: 400,
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      example: {
        error: 'Unauthorized',
        message: 'Authentication required',
        statusCode: 401,
      },
    },
  })
  async findByProjectId(@Request() req: any, @Param('projectId') projectId: string) {
    const userId = req.user.id

    return this.depositAddressesService.findByProjectId(projectId, userId)
  }
}

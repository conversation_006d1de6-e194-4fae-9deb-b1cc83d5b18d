import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '../../../config/env'
import { EmailTransportService, type EmailSenderConfig } from '../../notifications/channels/email'

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name)
  private readonly senderConfig: EmailSenderConfig

  constructor(private emailTransport: EmailTransportService) {
    const { mailCoachAuth } = getEnvConfig()
    this.senderConfig = {
      senderName: mailCoachAuth.senderName,
      senderEmail: mailCoachAuth.senderEmail,
    }
  }

  async sendOTP(
    email: string,
    otp: string,
    type: 'sign-in' | 'email-verification' | 'password-reset' = 'sign-in',
  ): Promise<any> {
    try {
      this.logger.log(`Attempting to send OTP email to: ${email}`)

      // Test account bypass for development/testing
      const envConfig = getEnvConfig()

      if (envConfig.testAccount && email === envConfig.testAccount.email) {
        this.logger.log(`Using test account bypass for ${email} - skipping actual email sending`)

        return { success: true, message: 'Test email bypassed' }
      }

      const subject = this.getSubjectForType(type)
      const html = this.generateOtpEmailTemplate(otp, type)

      const result = await this.emailTransport.sendEmail(
        {
          to: email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log('OTP email sent successfully')

      return result
    } catch (error) {
      this.logger.error('Failed to send OTP email:', error)
      throw error
    }
  }

  private getSubjectForType(type: 'sign-in' | 'email-verification' | 'password-reset'): string {
    switch (type) {
      case 'sign-in':
        return 'Your Sign-In Verification Code'
      case 'email-verification':
        return 'Verify Your Email Address'
      case 'password-reset':
        return 'Reset Your Password'
      default:
        return 'Your Verification Code'
    }
  }

  private generateOtpEmailTemplate(otp: string, type: string): string {
    let actionText: string

    if (type === 'sign-in') {
      actionText = 'sign in to your account'
    } else if (type === 'email-verification') {
      actionText = 'verify your email address'
    } else {
      actionText = 'reset your password'
    }

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333; margin-bottom: 10px;">Verification Code</h1>
          <p style="color: #666; font-size: 16px;">Use this code to ${actionText}</p>
        </div>
        
        <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; text-align: center; margin: 30px 0;">
          <div style="font-size: 36px; font-weight: bold; letter-spacing: 8px; color: #007bff; font-family: 'Courier New', monospace;">
            ${otp}
          </div>
        </div>
        
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
          <p style="margin: 0; color: #856404; font-size: 14px;">
            ⏰ <strong>This code expires in 5 minutes</strong>
          </p>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 13px; margin: 0;">
            If you didn't request this code, please ignore this email or contact support if you have concerns.
          </p>
        </div>
      </div>
    `
  }
}

// Singleton instance for Better Auth integration (outside NestJS DI container)
let emailServiceInstance: EmailService | null = null

export function getEmailService(): EmailService {
  if (!emailServiceInstance) {
    // Create instances manually since Better Auth runs outside NestJS DI
    const emailTransport = new EmailTransportService()
    emailServiceInstance = new EmailService(emailTransport)
  }

  return emailServiceInstance
}

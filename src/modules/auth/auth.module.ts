import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'

import { AuthController } from './auth.controller'
import { AuthService } from './auth.service'
import { AdminKeyGuard } from './guards/admin-key.guard'
import { CookieAuthGuard } from './guards/cookie-auth.guard'
import { EmailService } from './services/email.service'
import { SmsService } from './services/sms.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { EmailTransportService } from '../notifications/channels/email'
import { NotificationsModule } from '../notifications/notifications.module'

@Module({
  imports: [ConfigModule, DatabaseModule, NotificationsModule],
  controllers: [AuthController],
  providers: [AuthService, EmailService, SmsService, CookieAuthGuard, AdminKeyGuard, EmailTransportService],
  exports: [AuthService, CookieA<PERSON>G<PERSON>, Ad<PERSON><PERSON><PERSON>Guard, EmailService, SmsService],
})
export class AuthModule {}

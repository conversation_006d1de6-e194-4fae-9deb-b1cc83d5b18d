import { Controller, Post, Body, UseGuards, Request, Response, Get, Logger, Res, Inject } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse, ApiCookieAuth } from '@nestjs/swagger'
import { eq } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'
import { Request as ExpressRequest, Response as ExpressResponse } from 'express'

import { AuthService } from './auth.service'
import { SendEmailOtpDto } from './dto/send-email-otp.dto'
import { SendPhoneOtpDto } from './dto/send-phone-otp.dto'
import { VerifyEmailOtpDto } from './dto/verify-email-otp.dto'
import { VerifyPhoneOtpDto } from './dto/verify-phone-otp.dto'
import { CookieAuthGuard } from './guards/cookie-auth.guard'
import { SmsService } from './services/sms.service'
import { AuthRequest } from './types/auth-request.interface'
import { users } from '../../libs/database/schema/index.js'

@ApiTags('Authentication API')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name)

  constructor(
    private authService: AuthService,
    private smsService: SmsService,
    @Inject('DB') private db: NodePgDatabase<any>,
  ) {}

  // =====================
  // EMAIL OTP (Better Auth - Documentation Only)
  // =====================

  @Post('email-otp/send-verification-otp')
  @ApiOperation({
    summary: 'Send Email OTP (Better Auth)',
    description: 'Send a 6-digit OTP code to email address. This endpoint is automatically created by Better Auth.',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP sent successfully',
    schema: {
      example: {
        success: true,
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid email address',
    schema: {
      example: {
        error: {
          status: 400,
          message: 'Invalid email address',
        },
      },
    },
  })
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async sendEmailOtp(@Body() _dto: SendEmailOtpDto) {
    // This method is never called - it's for documentation only
    // The actual implementation is handled by Better Auth middleware
    throw new Error('This endpoint is handled by Better Auth middleware')
  }

  @Post('sign-in/email-otp')
  @ApiOperation({
    summary: 'Sign In with Email OTP (Better Auth)',
    description: 'Sign in and create session using email OTP. This endpoint is automatically created by Better Auth.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully signed in with email OTP',
    schema: {
      example: {
        user: {
          id: 'user123',
          email: '<EMAIL>',
          emailVerified: true,
          createdAt: '2025-06-04T14:21:26.352Z',
          updatedAt: '2025-06-04T14:21:26.352Z',
        },
        session: {
          id: 'session123',
          userId: 'user123',
          expiresAt: '2025-06-11T14:21:26.352Z',
          token: 'session_token_here',
          createdAt: '2025-06-04T14:21:26.352Z',
          updatedAt: '2025-06-04T14:21:26.352Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid OTP',
    schema: {
      example: {
        error: {
          status: 400,
          message: 'Invalid or expired verification code',
        },
      },
    },
  })
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async signInWithEmailOtp(@Body() _dto: VerifyEmailOtpDto) {
    // This method is never called - it's for documentation only
    // The actual implementation is handled by Better Auth middleware
    throw new Error('This endpoint is handled by Better Auth middleware')
  }

  // =====================
  // PHONE OTP
  // =====================

  @Post('phone-otp/send')
  @ApiOperation({
    summary: 'Send Phone OTP',
    description: 'Send a 6-digit OTP code to the specified phone number via SMS',
  })
  @ApiResponse({
    status: 200,
    description: 'OTP sent successfully',
    schema: {
      example: {
        success: true,
        message: 'Verification code sent to your phone',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid phone number format',
    schema: {
      example: {
        success: false,
        message: 'Invalid phone number format. Please use international format (e.g., +1234567890)',
      },
    },
  })
  async sendPhoneOtp(@Body() dto: SendPhoneOtpDto) {
    try {
      this.logger.log(`Sending OTP to phone: ${dto.phoneNumber}`)

      const result = await this.smsService.sendVerification(dto.phoneNumber)

      if (!result.success) {
        throw new Error('Failed to send verification code')
      }

      this.logger.log(`OTP sent successfully to ${dto.phoneNumber}`)

      return {
        success: true,
        message: 'Verification code sent to your phone',
      }
    } catch (error) {
      this.logger.error(`Failed to send OTP to ${dto.phoneNumber}:`, error)
      throw error
    }
  }

  @Post('phone-otp/link-with-session')
  @ApiCookieAuth('caishen-pro.session_token')
  @ApiOperation({
    summary: 'Link Phone with Session Cookie',
    description: 'Link a verified phone number using session cookie authentication',
  })
  @ApiResponse({
    status: 200,
    description: 'Phone number linked successfully',
    schema: {
      example: {
        success: true,
        message: 'Phone number linked successfully',
        user: {
          id: 'user123',
          phoneNumber: '+1234567890',
          phoneVerified: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid OTP or session',
    schema: {
      example: {
        success: false,
        message: 'Invalid verification code',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'No session or invalid session',
    schema: {
      example: {
        success: false,
        message: 'No session cookie found',
      },
    },
  })
  async linkPhoneWithSession(@Body() dto: VerifyPhoneOtpDto, @Request() req: any, @Res() res: ExpressResponse) {
    try {
      this.logger.log(`Linking phone ${dto.phoneNumber} with session`)

      // First verify the OTP with your SMS service
      const otpResult = await this.smsService.verifyCode(dto.phoneNumber, dto.otp)

      if (!otpResult.success || !otpResult.valid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid verification code',
        })
      }

      // Use AuthService to get session instead of manual parsing
      const betterAuthSession = await this.authService.getBetterAuthSession(req)

      if (!betterAuthSession?.user) {
        this.logger.error('No session found for phone verification')

        return res.status(401).json({
          success: false,
          message: 'No session cookie found',
        })
      }

      const userId = betterAuthSession.user.id
      this.logger.log(`Found valid session for user: ${userId}`)

      // Get current user info to check if phone is already set
      const currentUser = await this.db.select().from(users).where(eq(users.id, userId)).limit(1)

      if (currentUser.length === 0) {
        return res.status(401).json({
          success: false,
          message: 'User not found',
        })
      }

      // PHONE NUMBER RESTRICTION: Prevent changing phone number once set
      this.logger.log(`[DEBUG] Current user phone: ${currentUser[0].phoneNumber}`)
      this.logger.log(`[DEBUG] Requested phone: ${dto.phoneNumber}`)
      this.logger.log(`[DEBUG] Phone numbers match: ${currentUser[0].phoneNumber === dto.phoneNumber}`)

      if (currentUser[0].phoneNumber && currentUser[0].phoneNumber !== dto.phoneNumber) {
        this.logger.log(`[DEBUG] BLOCKING phone change from ${currentUser[0].phoneNumber} to ${dto.phoneNumber}`)

        return res.status(400).json({
          success: false,
          message: 'Phone number cannot be changed once set. Contact support if you need to update your phone number.',
        })
      }

      this.logger.log(`[DEBUG] Phone number restriction passed - allowing link`)

      // Check if phone is already linked to another user
      const phoneCheck = await this.db.select().from(users).where(eq(users.phoneNumber, dto.phoneNumber)).limit(1)

      if (phoneCheck.length > 0 && phoneCheck[0].id !== userId) {
        return res.status(400).json({
          success: false,
          message: 'Phone number is already linked to another account',
        })
      }

      // Update the user with phone number
      await this.db
        .update(users)
        .set({
          phoneNumber: dto.phoneNumber,
          phoneVerified: true,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId))

      this.logger.log(`Successfully linked phone ${dto.phoneNumber} to user ${userId}`)

      // Get updated user info
      const updatedUser = await this.db.select().from(users).where(eq(users.id, userId)).limit(1)

      return res.json({
        success: true,
        message: 'Phone number linked successfully',
        user: updatedUser[0],
      })
    } catch (error) {
      this.logger.error(`Failed to link phone with session:`, error)

      return res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to link phone number',
      })
    }
  }

  // =====================
  // SESSION MANAGEMENT
  // =====================

  @Get('session/user')
  @UseGuards(CookieAuthGuard)
  @ApiCookieAuth('caishen-pro.session_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get Current User',
    description: 'Get the current authenticated user information',
  })
  @ApiResponse({
    status: 200,
    description: 'User information retrieved successfully',
    schema: {
      example: {
        id: 'user123',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        phoneVerified: true,
        emailVerified: true,
        createdAt: '2025-06-04T14:21:26.352Z',
        updatedAt: '2025-06-04T14:21:26.352Z',
      },
    },
  })
  @ApiResponse({ status: 401, description: 'User not authenticated' })
  async getCurrentUser(@Request() req: AuthRequest) {
    return req.user
  }

  @Post('session/signout')
  @UseGuards(CookieAuthGuard)
  @ApiCookieAuth('caishen-pro.session_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Sign Out',
    description: 'Sign out the current user and invalidate the session',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully signed out',
    schema: {
      example: {
        success: true,
        message: 'Successfully signed out',
      },
    },
  })
  @ApiResponse({ status: 401, description: 'User not authenticated' })
  async signOut(@Request() req: ExpressRequest, @Response({ passthrough: true }) res: ExpressResponse) {
    return this.authService.signOut(req, res)
  }
}

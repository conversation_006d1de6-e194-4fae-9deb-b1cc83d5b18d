import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, ForbiddenException } from '@nestjs/common'

@Injectable()
export class AdminKeyGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest()

    // Get admin key from request headers
    const adminKey = request.headers['x-admin-key'] || request.headers['admin-key']

    // Check if admin key is provided in environment
    const envAdminKey = process.env.FORM_ADMIN_KEY

    if (!envAdminKey) {
      throw new ForbiddenException('Admin access is not configured on this server')
    }

    if (!adminKey) {
      throw new UnauthorizedException('Admin key is required. Please provide X-Admin-Key header.')
    }

    if (adminKey !== envAdminKey) {
      throw new UnauthorizedException('Invalid admin key')
    }

    return true
  }
}

import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common'

import { AuthService } from '../auth.service'
import { AuthRequest } from '../types/auth-request.interface'

@Injectable()
export class CookieAuthGuard implements CanActivate {
  constructor(private authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthRequest>()

    // Use Better Auth session (cookie-based)
    const betterAuthSession = await this.authService.getBetterAuthSession(request)

    if (betterAuthSession?.user) {
      // Set the complete user object with all fields
      request.user = {
        id: betterAuthSession.user.id,
        email: betterAuthSession.user.email,
        name: betterAuthSession.user.name,
        emailVerified: betterAuthSession.user.emailVerified,
        phoneNumber: betterAuthSession.user.phoneNumber,
        phoneVerified: betterAuthSession.user.phoneVerified,
        createdAt: betterAuthSession.user.createdAt,
        updatedAt: betterAuthSession.user.updatedAt,
        // Add sessionId for compatibility if needed elsewhere
        sessionId: betterAuthSession.session?.id,
      }

      return true
    }

    throw new UnauthorizedException('Authentication required')
  }
}

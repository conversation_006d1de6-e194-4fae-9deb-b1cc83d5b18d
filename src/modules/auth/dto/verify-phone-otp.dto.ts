import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const verifyPhoneOtpSchema = z.object({
  phoneNumber: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format'),
  otp: z.string().length(6, 'OTP must be 6 digits'),
})

export class VerifyPhoneOtpDto extends createZodDto(verifyPhoneOtpSchema) {
  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number in international format',
  })
  phoneNumber!: string

  @ApiProperty({
    example: '123456',
    description: '6-digit OTP code received via SMS',
  })
  otp!: string
}

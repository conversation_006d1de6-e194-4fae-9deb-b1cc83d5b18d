import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const sendEmailOtpSchema = z.object({
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  type: z.enum(['sign-in', 'email-verification', 'password-reset']).default('sign-in'),
})

export class SendEmailOtpDto extends createZodDto(sendEmailOtpSchema) {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address to send the OTP to',
  })
  email!: string

  @ApiProperty({
    example: 'sign-in',
    enum: ['sign-in', 'email-verification', 'password-reset'],
    default: 'sign-in',
    description: 'Type of OTP email to send (determines email template and purpose)',
  })
  type!: 'sign-in' | 'email-verification' | 'password-reset'
}

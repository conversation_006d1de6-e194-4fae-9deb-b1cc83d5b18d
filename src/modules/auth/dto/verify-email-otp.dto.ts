import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const verifyEmailOtpSchema = z.object({
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  otp: z.string().length(6, 'OTP must be 6 digits'),
})

export class VerifyEmailOtpDto extends createZodDto(verifyEmailOtpSchema) {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address that received the OTP',
  })
  email!: string

  @ApiProperty({
    example: '123456',
    description: '6-digit OTP code received via email',
  })
  otp!: string
}

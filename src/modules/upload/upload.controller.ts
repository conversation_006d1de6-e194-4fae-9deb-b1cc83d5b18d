import { Controller, Post, Delete, Get, Body, Param, UseGuards, BadRequestException } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiCookieAuth, ApiBearerAuth } from '@nestjs/swagger'
import { z } from 'zod'

import { UploadService, PresignedUrlResponse, AllowedMimeTypes } from './upload.service'
import { CookieAuthGuard } from '../auth/guards/cookie-auth.guard'

// Request DTOs using Zod
const GenerateUploadUrlSchema = z.object({
  filename: z.string().min(1).max(255),
  contentType: z.string().min(1),
  projectId: z.string().optional(),
  uploadType: z.enum(['logo', 'banner', 'team-photo', 'document', 'whitepaper']),
})

const DeleteFileSchema = z.object({
  fileKey: z.string().min(1),
})

type GenerateUploadUrlDto = z.infer<typeof GenerateUploadUrlSchema>

type DeleteFileDto = z.infer<typeof DeleteFileSchema>

@ApiTags('Upload')
@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('presigned-url')
  @UseGuards(CookieAuthGuard)
  @ApiCookieAuth('caishen-pro.session_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Generate presigned URL for file upload',
    description: `
      Generates a secure presigned URL for uploading files directly to S3.
      
      **Upload Process:**
      1. Call this endpoint to get a presigned URL
      2. Use the returned URL to upload your file directly to S3
      3. Save the returned \`publicUrl\` in your application
      
      **Supported File Types:**
      - **Images**: JPEG, PNG, WebP, SVG (max 10MB, SVG max 2MB)
      - **Documents**: PDF (max 50MB), DOC/DOCX (max 25MB)
      
      **Upload Types:**
      - \`logo\`: Project logo images
      - \`banner\`: Project banner/header images  
      - \`team-photo\`: Team member photos
      - \`document\`: General documents
      - \`whitepaper\`: Project whitepapers
    `,
  })
  @ApiBody({
    description: 'Upload request details',
    examples: {
      logo: {
        summary: 'Project Logo',
        value: {
          filename: 'project-logo.png',
          contentType: 'image/png',
          projectId: 'proj_abc123xyz789',
          uploadType: 'logo',
        },
      },
      whitepaper: {
        summary: 'Project Whitepaper',
        value: {
          filename: 'whitepaper.pdf',
          contentType: 'application/pdf',
          projectId: 'proj_abc123xyz789',
          uploadType: 'whitepaper',
        },
      },
      teamPhoto: {
        summary: 'Team Member Photo',
        value: {
          filename: 'john-doe.jpg',
          contentType: 'image/jpeg',
          projectId: 'proj_abc123xyz789',
          uploadType: 'team-photo',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Presigned URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        uploadUrl: {
          type: 'string',
          description: 'Presigned URL for direct upload to S3 (valid for 5 minutes)',
          example:
            'https://your-bucket.s3.us-east-1.amazonaws.com/projects/proj_123/logo/2024-01-15/abc123_project-logo.png?X-Amz-Algorithm=...',
        },
        fileKey: {
          type: 'string',
          description: 'S3 file key for reference',
          example: 'projects/proj_123/logo/2024-01-15/abc123_project-logo.png',
        },
        publicUrl: {
          type: 'string',
          description: 'Public URL to access the file after upload',
          example:
            'https://your-bucket.s3.us-east-1.amazonaws.com/projects/proj_123/logo/2024-01-15/abc123_project-logo.png',
        },
        expiresIn: {
          type: 'number',
          description: 'URL expiration time in seconds',
          example: 300,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request - unsupported file type, wrong extension, etc.',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Unsupported file type: text/plain. Allowed types: image/jpeg, image/png, application/pdf',
        },
        error: { type: 'string', example: 'Bad Request' },
        statusCode: { type: 'number', example: 400 },
      },
    },
  })
  async generatePresignedUrl(@Body() body: GenerateUploadUrlDto): Promise<PresignedUrlResponse> {
    const validation = GenerateUploadUrlSchema.safeParse(body)

    if (!validation.success) {
      throw new BadRequestException('Invalid request data')
    }

    return this.uploadService.generatePresignedUrl(validation.data)
  }

  @Delete('file')
  @UseGuards(CookieAuthGuard)
  @ApiCookieAuth('caishen-pro.session_token')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete uploaded file',
    description: `
      Permanently deletes a file from S3 storage.
      
      **⚠️ Warning:** This action cannot be undone.
      
      Use the \`fileKey\` returned from the upload process to delete the file.
    `,
  })
  @ApiBody({
    description: 'File deletion request',
    examples: {
      deleteFile: {
        summary: 'Delete File',
        value: {
          fileKey: 'projects/proj_123/logo/2024-01-15/abc123_project-logo.png',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'File deleted successfully' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Failed to delete file',
  })
  async deleteFile(@Body() body: DeleteFileDto): Promise<{ success: boolean; message: string }> {
    const validation = DeleteFileSchema.safeParse(body)

    if (!validation.success) {
      throw new BadRequestException('Invalid file key')
    }

    return this.uploadService.deleteFile(validation.data.fileKey)
  }

  @Get('file-types')
  @ApiOperation({
    summary: 'Get supported file types',
    description: 'Returns information about all supported file types, their extensions, and size limits.',
  })
  @ApiResponse({
    status: 200,
    description: 'Supported file types information',
    schema: {
      type: 'object',
      example: {
        'image/jpeg': {
          extensions: ['.jpg', '.jpeg'],
          maxSize: 10485760,
        },
        'image/png': {
          extensions: ['.png'],
          maxSize: 10485760,
        },
        'application/pdf': {
          extensions: ['.pdf'],
          maxSize: 52428800,
        },
      },
    },
  })
  async getSupportedFileTypes(): Promise<{
    supportedTypes: AllowedMimeTypes
    uploadTypes: string[]
    usage: Record<string, string>
  }> {
    return {
      supportedTypes: this.uploadService.getAllowedFileTypes(),
      uploadTypes: ['logo', 'banner', 'team-photo', 'document', 'whitepaper'],
      usage: {
        logo: 'Project logo images (recommended: 400x400px or 1:1 aspect ratio)',
        banner: 'Project header/banner images (recommended: 1200x400px or 3:1 aspect ratio)',
        'team-photo': 'Team member profile photos (recommended: 300x300px or 1:1 aspect ratio)',
        document: 'General project documents and files',
        whitepaper: 'Project whitepapers and technical documentation',
      },
    }
  }

  @Get('validate-url/:encodedUrl')
  @ApiOperation({
    summary: 'Validate file URL',
    description: 'Check if a file URL belongs to our S3 bucket and is valid.',
  })
  @ApiResponse({
    status: 200,
    description: 'URL validation result',
    schema: {
      type: 'object',
      properties: {
        isValid: { type: 'boolean' },
        fileKey: { type: 'string', nullable: true },
      },
    },
  })
  async validateFileUrl(@Param('encodedUrl') encodedUrl: string): Promise<{
    isValid: boolean
    fileKey: string | null
  }> {
    try {
      const url = decodeURIComponent(encodedUrl)
      const fileKey = this.uploadService.extractFileKeyFromUrl(url)

      return {
        isValid: fileKey !== null,
        fileKey,
      }
    } catch {
      return {
        isValid: false,
        fileKey: null,
      }
    }
  }
}

import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { Injectable, BadRequestException, Logger } from '@nestjs/common'
import { nanoid } from 'nanoid'

import { getEnvConfig } from '../../config/env'

export interface PresignedUrlRequest {
  filename: string
  contentType: string
  projectId?: string
  uploadType: 'logo' | 'banner' | 'team-photo' | 'document' | 'whitepaper'
}

export interface PresignedUrlResponse {
  uploadUrl: string
  fileKey: string
  publicUrl: string
  expiresIn: number
}

export interface FileTypeInfo {
  extensions: string[]
  maxSize: number
}

export type AllowedMimeTypes = Record<string, FileTypeInfo>

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name)
  private readonly s3Client: S3Client
  private readonly bucketName: string

  // Allowed file types and their MIME types
  private readonly allowedMimeTypes: AllowedMimeTypes = {
    // Images
    'image/jpeg': { extensions: ['.jpg', '.jpeg'], maxSize: 10 * 1024 * 1024 }, // 10MB
    'image/png': { extensions: ['.png'], maxSize: 10 * 1024 * 1024 }, // 10MB
    'image/webp': { extensions: ['.webp'], maxSize: 10 * 1024 * 1024 }, // 10MB
    'image/svg+xml': { extensions: ['.svg'], maxSize: 2 * 1024 * 1024 }, // 2MB
    // Documents
    'application/pdf': { extensions: ['.pdf'], maxSize: 50 * 1024 * 1024 }, // 50MB
    'application/msword': { extensions: ['.doc'], maxSize: 25 * 1024 * 1024 }, // 25MB
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
      extensions: ['.docx'],
      maxSize: 25 * 1024 * 1024,
    }, // 25MB
  }

  constructor() {
    const config = getEnvConfig()

    this.s3Client = new S3Client({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    })

    this.bucketName = config.aws.s3BucketName
    this.logger.log(`Upload service initialized with bucket: ${this.bucketName}`)
  }

  async generatePresignedUrl(request: PresignedUrlRequest): Promise<PresignedUrlResponse> {
    const { filename, contentType, projectId, uploadType } = request

    // Validate content type
    if (!this.allowedMimeTypes[contentType]) {
      throw new BadRequestException(
        `Unsupported file type: ${contentType}. Allowed types: ${Object.keys(this.allowedMimeTypes).join(', ')}`,
      )
    }

    // Validate file extension matches content type
    const fileInfo = this.allowedMimeTypes[contentType]
    const hasValidExtension = fileInfo.extensions.some((ext: string) => filename.toLowerCase().endsWith(ext))

    if (!hasValidExtension) {
      throw new BadRequestException(
        `File extension doesn't match content type. Expected: ${fileInfo.extensions.join(', ')}`,
      )
    }

    // Generate unique file key
    const timestamp = new Date().toISOString().substring(0, 10) // YYYY-MM-DD
    const uniqueId = nanoid(12)
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')

    let fileKey: string

    if (projectId) {
      fileKey = `projects/${projectId}/${uploadType}/${timestamp}/${uniqueId}_${sanitizedFilename}`
    } else {
      fileKey = `uploads/${uploadType}/${timestamp}/${uniqueId}_${sanitizedFilename}`
    }

    // Create S3 PutObject command
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: fileKey,
      ContentType: contentType,
      ServerSideEncryption: 'AES256',
      Metadata: {
        originalFilename: filename,
        uploadType,
        projectId: projectId || 'unknown',
        uploadedAt: new Date().toISOString(),
      },
    })

    try {
      // Generate presigned URL (valid for 5 minutes)
      const uploadUrl = await getSignedUrl(this.s3Client, command, { expiresIn: 300 })

      // Construct public URL
      const publicUrl = `https://${this.bucketName}.s3.${getEnvConfig().aws.region}.amazonaws.com/${fileKey}`

      this.logger.log(`Generated presigned URL for ${uploadType}: ${fileKey}`)

      return {
        uploadUrl,
        fileKey,
        publicUrl,
        expiresIn: 300, // 5 minutes
      }
    } catch (error) {
      this.logger.error('Failed to generate presigned URL:', error)
      throw new BadRequestException('Failed to generate upload URL')
    }
  }

  async deleteFile(fileKey: string): Promise<{ success: boolean; message: string }> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: fileKey,
      })

      await this.s3Client.send(command)

      this.logger.log(`Deleted file: ${fileKey}`)

      return {
        success: true,
        message: 'File deleted successfully',
      }
    } catch (error) {
      this.logger.error(`Failed to delete file ${fileKey}:`, error)
      throw new BadRequestException('Failed to delete file')
    }
  }

  /**
   * Extract file key from public URL
   */
  extractFileKeyFromUrl(publicUrl: string): string | null {
    try {
      const url = new URL(publicUrl)
      const bucketDomain = `${this.bucketName}.s3.${getEnvConfig().aws.region}.amazonaws.com`

      if (url.hostname === bucketDomain) {
        return url.pathname.substring(1) // Remove leading slash
      }

      return null
    } catch {
      return null
    }
  }

  /**
   * Validate if a file URL belongs to our bucket
   */
  isValidFileUrl(url: string): boolean {
    const fileKey = this.extractFileKeyFromUrl(url)

    return fileKey !== null
  }

  /**
   * Get file info for validation
   */
  getFileTypeInfo(contentType: string): FileTypeInfo | null {
    return this.allowedMimeTypes[contentType] || null
  }

  /**
   * Get all allowed file types for documentation
   */
  getAllowedFileTypes(): AllowedMimeTypes {
    return this.allowedMimeTypes
  }
}

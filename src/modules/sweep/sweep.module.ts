import { Module, forwardRef } from '@nestjs/common'

import { CryptoModule } from '@/libs/crypto/crypto.module'
import { DatabaseModule } from '@/libs/database/database.module'

import { SweepController } from './sweep.controller'
import { SweepService } from './sweep.service'
import { AuthModule } from '../auth/auth.module'
import { BlockchainModule } from '../blockchain/blockchain.module'
import { PoolModule } from '../pool/pool.module'
import { ProjectsModule } from '../projects/projects.module'

@Module({
  imports: [
    DatabaseModule,
    CryptoModule,
    AuthModule,
    BlockchainModule,
    forwardRef(() => PoolModule),
    forwardRef(() => ProjectsModule),
  ],
  controllers: [SweepController],
  providers: [SweepService],
  exports: [SweepService],
})
export class SweepModule {}

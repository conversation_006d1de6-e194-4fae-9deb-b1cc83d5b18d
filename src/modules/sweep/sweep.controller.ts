import { <PERSON>, <PERSON>, Get, Param, Logger } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger'

import { SweepService } from './sweep.service'

@ApiTags('Sweep')
@Controller('sweep')
export class SweepController {
  private readonly logger = new Logger(SweepController.name)

  constructor(private readonly sweepService: SweepService) {}

  @Post('sweep-all')
  @ApiOperation({
    summary: 'Sweep all pending deposit addresses',
    description:
      'Administrative endpoint to sweep all pending deposit addresses with available balances. Requires authentication.',
  })
  @ApiResponse({
    status: 200,
    description: 'Sweep operation completed successfully',
    schema: {
      example: {
        success: true,
        message: 'Sweep operation completed',
        results: {
          totalAddresses: 5,
          sweptAddresses: 3,
          failedAddresses: 0,
          skippedAddresses: 2,
          totalAmountSwept: '1.5',
          currency: 'ETH',
          transactions: [
            {
              addressId: 'addr_123',
              publicAddress: '0x1234567890abcdef...',
              amount: '0.5',
              txHash: '0xabcdef1234567890...',
              status: 'success',
            },
            {
              addressId: 'addr_456',
              publicAddress: '0x9876543210fedcba...',
              amount: '1.0',
              txHash: '0xfedcba0987654321...',
              status: 'success',
            },
          ],
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      example: {
        error: 'Unauthorized',
        message: 'Authentication required',
        statusCode: 401,
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during sweep operation',
    schema: {
      example: {
        error: 'Internal Server Error',
        message: 'Failed to complete sweep operation',
        statusCode: 500,
      },
    },
  })
  async sweepAllPending() {
    this.logger.log('Manual sweep-all operation triggered')

    return this.sweepService.sweepAllPendingAddresses()
  }

  @Post('sweep/:addressId')
  @ApiOperation({
    summary: 'Sweep a specific deposit address by ID',
    description:
      'Administrative endpoint to sweep funds from a specific deposit address by its ID. Requires authentication.',
  })
  @ApiParam({
    name: 'addressId',
    description: 'The unique identifier of the deposit address to sweep',
    example: 'addr_123',
  })
  @ApiResponse({
    status: 200,
    description: 'Sweep operation completed successfully',
    schema: {
      example: {
        success: true,
        message: 'Sweep operation completed for address addr_123',
        result: {
          addressId: 'addr_123',
          publicAddress: '0x1234567890abcdef...',
          amount: '0.5',
          currency: 'ETH',
          txHash: '0xabcdef1234567890...',
          status: 'success',
          timestamp: '2025-01-23T10:00:00Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid address ID or address not found',
    schema: {
      example: {
        error: 'Bad Request',
        message: 'Deposit address not found or invalid address ID',
        statusCode: 400,
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      example: {
        error: 'Unauthorized',
        message: 'Authentication required',
        statusCode: 401,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Deposit address not found',
    schema: {
      example: {
        error: 'Not Found',
        message: 'Deposit address with ID addr_123 not found',
        statusCode: 404,
      },
    },
  })
  async sweepSpecific(@Param('addressId') addressId: string) {
    this.logger.log(`Manual sweep operation triggered for address: ${addressId}`)

    return this.sweepService.sweepSpecificAddress(addressId)
  }

  @Post('sweep-by-address/:publicAddress')
  @ApiOperation({
    summary: 'Sweep a specific deposit address by public address',
    description:
      'Administrative endpoint to sweep funds from a specific deposit address by its public address (wallet address). Requires authentication.',
  })
  @ApiParam({
    name: 'publicAddress',
    description: 'The public wallet address to sweep (e.g., 0x... for EVM or base58 for Solana)',
    example: '******************************************',
  })
  @ApiResponse({
    status: 200,
    description: 'Sweep operation completed successfully',
    schema: {
      example: {
        success: true,
        message: 'Sweep operation completed for address 0x1234567890abcdef...',
        result: {
          addressId: 'addr_123',
          publicAddress: '******************************************',
          amount: '0.5',
          currency: 'USDC',
          txHash: '******************************************',
          status: 'success',
          timestamp: '2025-01-23T10:00:00Z',
          launchFeePaid: true,
          poolCreation: {
            attempted: true,
            success: true,
            poolAddress: '******************************************',
            txHash: '******************************************',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid public address format',
    schema: {
      example: {
        error: 'Bad Request',
        message: 'Invalid public address format',
        statusCode: 400,
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      example: {
        error: 'Unauthorized',
        message: 'Authentication required',
        statusCode: 401,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Public address not found in system',
    schema: {
      example: {
        error: 'Not Found',
        message: 'Public address 0x1234567890abcdef... not found in system',
        statusCode: 404,
      },
    },
  })
  async sweepByPublicAddress(@Param('publicAddress') publicAddress: string) {
    this.logger.log(`Manual sweep operation triggered for public address: ${publicAddress}`)

    const result = await this.sweepService.sweepByPublicAddress(publicAddress)

    // Enhanced response with better messaging about the full flow
    let message = result.success
      ? `Sweep operation completed for address ${publicAddress}`
      : `Sweep operation failed for address ${publicAddress}: ${result.error}`

    if (result.success) {
      if (result.launchFeePaid) {
        message += '. Launch fee paid!'
      }

      if (result.poolCreation?.attempted) {
        message += result.poolCreation.success ? ' Pool created successfully!' : ' Pool creation attempted but failed.'
      }
    }

    return {
      success: result.success,
      message,
      result,
    }
  }

  @Get('status')
  @ApiOperation({
    summary: 'Get sweep status for all addresses',
    description:
      'Administrative endpoint to retrieve the current sweep status and statistics for all deposit addresses. Requires authentication.',
  })
  @ApiResponse({
    status: 200,
    description: 'Sweep status retrieved successfully',
    schema: {
      example: {
        success: true,
        status: {
          totalAddresses: 10,
          pendingAddresses: 3,
          sweptAddresses: 6,
          failedAddresses: 1,
          lastSweepTime: '2025-01-23T09:30:00Z',
          nextScheduledSweep: '2025-01-23T10:30:00Z',
          totalSweptAmount: '5.25',
          currency: 'ETH',
          addresses: [
            {
              id: 'addr_123',
              publicAddress: '0x1234567890abcdef...',
              status: 'swept',
              lastSweptAt: '2025-01-23T09:15:00Z',
              balance: '0.0',
              sweptAmount: '0.5',
            },
            {
              id: 'addr_456',
              publicAddress: '0x9876543210fedcba...',
              status: 'pending',
              balance: '1.2',
              lastChecked: '2025-01-23T09:25:00Z',
            },
            {
              id: 'addr_789',
              publicAddress: '0xabcdef1234567890...',
              status: 'failed',
              balance: '0.3',
              lastFailure: '2025-01-23T09:20:00Z',
              errorMessage: 'Insufficient gas',
            },
          ],
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
    schema: {
      example: {
        error: 'Unauthorized',
        message: 'Authentication required',
        statusCode: 401,
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to retrieve sweep status',
    schema: {
      example: {
        error: 'Internal Server Error',
        message: 'Failed to retrieve sweep status',
        statusCode: 500,
      },
    },
  })
  async getSweepStatus() {
    return this.sweepService.getSweepStatus()
  }
}

import { Injectable } from '@nestjs/common'

import { type Country } from './schemas/country.schema'
import { type FiatCurrency } from './schemas/fiat-currency.schema'
import { type Offer } from './schemas/offer.schema'
import { RampStorage } from './storage'

@Injectable()
export class RampService {
  constructor(private storage: RampStorage) {}

  async getRampData(): Promise<{ fiatCurrencies: FiatCurrency[]; countries: Country[]; offers: Offer[] }> {
    return this.storage.getRampData()
  }

  async getFiatCurrencies(): Promise<FiatCurrency[]> {
    return this.storage.getFiatCurrencies()
  }

  async getCountries(): Promise<Country[]> {
    return this.storage.getCountries()
  }

  async getOffers(): Promise<Offer[]> {
    return this.storage.getOffers()
  }

  async setNewOffers(offers: Offer[]): Promise<void> {
    await this.storage.setNewOffers(offers)
  }
}

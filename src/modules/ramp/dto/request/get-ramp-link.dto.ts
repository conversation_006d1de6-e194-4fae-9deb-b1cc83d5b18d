import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class GetRampLinkRequestDto {
  @ApiProperty({
    description: 'Onramp/offramp provider',
    example: 'YellowCard',
    required: true,
    type: String,
  })
  @IsString()
  providerName: string

  @ApiProperty({
    description: 'Onramp/offramp link',
    example: 'YellowCard',
    required: true,
    type: String,
  })
  @IsString()
  linkTemplate: string

  @ApiProperty({
    description: 'User address',
    required: true,
    type: String,
  })
  @IsString()
  userAddress: string

  @ApiProperty({
    description: 'Ticker',
    required: true,
    example: 'USDT',
    type: String,
  })
  @IsString()
  ticker: string
}

import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsBoolean, ValidateNested } from 'class-validator'

import { CountryDto } from '../common/country.dto'
import { FiatCurrencyDto } from '../common/fiat-currency.dto'
import { OfferDto } from '../common/offer.dto'

class RampDataDto {
  @ApiProperty({
    description: 'Array of fiat currencies',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FiatCurrencyDto)
  fiatCurrencies: FiatCurrencyDto[]

  @ApiProperty({
    description: 'Array of available offers',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OfferDto)
  offers: OfferDto[]

  @ApiProperty({
    description: 'Array of available countries',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CountryDto)
  countries: CountryDto[]
}

export class GetRampDataResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({ type: RampDataDto })
  @ValidateNested()
  @Type(() => RampDataDto)
  data: RampDataDto
}

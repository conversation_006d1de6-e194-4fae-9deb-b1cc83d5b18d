import { z } from 'zod'

export const paymentMethodSchema = z.string()

export type PaymentMethod = z.infer<typeof paymentMethodSchema>

export const offerSchema = z.object({
  id: z.string(),
  fiatCurrency: z.string(),
  tokenId: z.string(),
  chainId: z.string(),
  paymentMethods: z.array(paymentMethodSchema),
  providerName: z.string(),
  providerLogoUrl: z.string(),
  providerFees: z.string().nullable(),
  onrampUrl: z.string().nullable(),
  offrampUrl: z.string().nullable(),
})

export type Offer = z.infer<typeof offerSchema>

import { createHmac } from 'crypto'

import { Body, Controller, Get, Logger, Post } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { getEnvConfig } from '@/config/env'

import { GetRampLinkRequestDto } from './dto/request/get-ramp-link.dto'
import { GetRampDataResponseDto } from './dto/response/get-ramp-data.dto'
import { GetRampLinkResponseDto } from './dto/response/get-ramp-link.dto'
import { USER_ADDRESS_PARAM, YELLOW_CARD_SIGNATURE_PARAM } from './ramp.constants'
import { RampService } from './ramp.service'

@ApiTags('Ramp')
@Controller({
  path: '/ramp',
  version: '1',
})
export class RampController {
  private logger = new Logger('RampController')

  constructor(private readonly rampsService: RampService) {}

  @ApiOperation({ description: 'Get all ramp data' })
  @ApiOkResponse({ type: GetRampDataResponseDto })
  @Get('/')
  async getRampData(): Promise<GetRampDataResponseDto> {
    this.logger.log(`Get all ramps data`)
    const { fiatCurrencies, offers, countries } = await this.rampsService.getRampData()

    return { success: true, data: { fiatCurrencies, offers, countries } }
  }

  @ApiOperation({ description: 'Get link to onramp provider' })
  @ApiOkResponse({ type: GetRampLinkResponseDto })
  @Post('/get-link')
  async getLink(@Body() body: GetRampLinkRequestDto) {
    const { linkTemplate, ticker, userAddress, providerName } = body
    let link = linkTemplate

    if (!link) throw new Error('Could not find offer link')

    link = link.replace(USER_ADDRESS_PARAM, userAddress)
    const { yellowCard, moonPay } = getEnvConfig()

    if (providerName.toLowerCase() === yellowCard.name.toLowerCase()) {
      const token = ticker.toUpperCase()
      const message = { address: userAddress, token }
      const signature = createHmac('sha256', yellowCard.secretKey).update(JSON.stringify(message)).digest('base64')

      link = link.replace(YELLOW_CARD_SIGNATURE_PARAM, encodeURIComponent(signature))
    }

    if (providerName.toLowerCase() === moonPay.name.toLowerCase()) {
      const url = new URL(link)
      const signature = createHmac('sha256', moonPay.secretKey).update(url.search).digest('base64')

      link = `${url.toString()}&signature=${encodeURIComponent(signature)}`
    }

    return { success: true, data: link }
  }
}

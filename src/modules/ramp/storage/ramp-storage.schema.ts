import { z } from 'zod'

export const countryDataSchema = z.object({
  name: z.string(),
  alpha2Code: z.string(),
  alpha3Code: z.string(),
  phone_code: z.string(),
  phone_codes: z.array(z.string()),
  flag_url: z.string(),
  currencies: z.array(z.string()),
})

export const onrampDataSchema = z.object({
  currency: z.string(),
  coinId: z.string(),
  chainId: z.string(),
  paymentMethods: z.array(z.string()),
  providerName: z.string(),
  providerLogoUrl: z.string(),
  providerFees: z.string().nullable(),
  onrampUrl: z.string().nullable(),
  offrampUrl: z.string().nullable(),
})

export const localCurrencySchema = z.object({
  code: z.string(),
  name: z.string(),
  symbol: z.string(),
  countries: z.array(z.string()),
  flag_url: z.string(),
})

export const getCountriesResponseSchema = z.object({
  countries: z.array(countryDataSchema),
})

export const getLocalTokensResponseSchema = z.object({
  currencies: z.array(localCurrencySchema),
})

export type GetCountriesResponse = z.infer<typeof getCountriesResponseSchema>
export type GetLocalCurrenciesResponse = z.infer<typeof getLocalTokensResponseSchema>
export type OnrampData = z.infer<typeof onrampDataSchema>

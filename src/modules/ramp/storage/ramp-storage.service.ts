import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

import { Storage } from '@/abstract/storage'
import { splitIntoChunks } from '@/utils/arrays/split-into-chunks'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  countryDataSchema,
  localCurrencySchema,
  onrampDataSchema,
  type GetCountriesResponse,
  type GetLocalCurrenciesResponse,
  type OnrampData,
} from './ramp-storage.schema'
import { FirebaseService } from '../../firebase'
import { type Country, countrySchema } from '../schemas/country.schema'
import { type FiatCurrency, fiatCurrencySchema } from '../schemas/fiat-currency.schema'
import { type PaymentMethod, type Offer, offerSchema } from '../schemas/offer.schema'

@Injectable()
export class RampStorage extends Storage implements OnModuleInit {
  private logger = new Logger('RampStorage')

  private fiatCurrencies: FiatCurrency[] = []
  private countries: Country[] = []
  private offers: Offer[] = []

  constructor(private firebase: FirebaseService) {
    super()
  }

  onModuleInit() {
    this.fetchRamp().catch((error) => this.logger.error(`Error on init RampStorage ${getErrorMessage(error)}`, error))
  }

  async revalidateCache(): Promise<void> {
    if (!this.isInitialized) return

    super.revalidateCache()
    this.fiatCurrencies = []
    this.countries = []
    this.offers = []
    this.onModuleInit()
  }

  @Cron(CronExpression.EVERY_HOUR, {
    waitForCompletion: true,
    name: 'updateRamp',
  })
  async updateRamp(): Promise<void> {
    try {
      await this.fetchRamp()
    } catch (e) {
      this.logger.error(`Interval updateRamp method failed`)
    }
  }

  async getRampData(): Promise<{ fiatCurrencies: FiatCurrency[]; countries: Country[]; offers: Offer[] }> {
    if (!this.isInitialized) await this.waitForInitPromise

    return {
      fiatCurrencies: this.fiatCurrencies,
      countries: this.countries,
      offers: this.offers,
    }
  }

  async getFiatCurrencies(): Promise<FiatCurrency[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    return this.fiatCurrencies
  }

  async getCountries(): Promise<Country[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    return this.countries
  }

  async getOffers(): Promise<Offer[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    return this.offers
  }

  async setNewOffers(offers: Offer[]): Promise<void> {
    await this.firebase.deleteAllDocumentsInCollection('onrampData')

    const chunks = splitIntoChunks(offers)

    for (const chunk of chunks) {
      try {
        const offersToSave: { data: OnrampData; id: string }[] = chunk.map((offer) => ({
          id: offer.id,
          data: {
            currency: offer.fiatCurrency,
            coinId: offer.tokenId,
            chainId: offer.chainId,
            paymentMethods: offer.paymentMethods,
            providerName: offer.providerName,
            providerLogoUrl: offer.providerLogoUrl,
            providerFees: offer.providerFees,
            onrampUrl: offer.onrampUrl,
            offrampUrl: offer.offrampUrl,
          },
        }))

        await this.firebase.saveDocuments('onrampData', offersToSave)
      } catch (e) {
        this.logger.error(e)
      }
    }

    this.logger.log(`Saved ${offers.length} ramp offers. Chunks count: ${chunks.length}`)

    void this.revalidateCache()
  }

  private async fetchRamp(): Promise<void> {
    try {
      const [fiatCurrencies, countries, offers] = await Promise.all([
        this.fetchFiatCurrencies(),
        this.fetchCountries(),
        this.fetchOnrampData(),
      ])

      this.fiatCurrencies = fiatCurrencies
      this.countries = countries
      this.offers = offers
      this.setIsInitialized()
      this.logger.log('Fetched ramp data from firebase')
    } catch (e) {
      this.logger.error(`Failed to fetch ramp data from firebase: ${getErrorMessage(e)}`)

      throw e
    }
  }

  private async fetchFiatCurrencies(): Promise<FiatCurrency[]> {
    const currencies = await this.firebase.getDocument<GetLocalCurrenciesResponse>('localCurrencies', 'allCurrencies')

    if (!currencies) throw new Error('Fiat currencies not found')

    return currencies.currencies.reduce((acc, token) => {
      const responseParse = localCurrencySchema.safeParse(token)

      if (!responseParse.success) {
        this.logger.error(responseParse.error)

        return acc
      }

      const mapped: FiatCurrency = {
        code: token.code,
        name: token.name,
        symbol: token.symbol,
        countries: token.countries,
        flagUrl: token.flag_url,
      }

      const resultParse = fiatCurrencySchema.safeParse(mapped)

      if (!resultParse.success) {
        this.logger.error(resultParse.error)

        return acc
      }

      return [...acc, mapped]
    }, [] as FiatCurrency[])
  }

  private async fetchCountries(): Promise<Country[]> {
    const countries = await this.firebase.getDocument<GetCountriesResponse>('countries', 'allCountries')

    if (!countries) throw new Error('Countries not found')

    return countries.countries.reduce((acc, country) => {
      const responseParse = countryDataSchema.safeParse(country)

      if (!responseParse.success) {
        this.logger.error(responseParse.error)

        return acc
      }

      const mapped: Country = {
        name: country.name,
        alpha2Code: country.alpha2Code,
        alpha3Code: country.alpha3Code,
        phoneCodes: country.phone_codes,
        flagUrl: country.flag_url,
        supportedTokens: country.currencies,
      }

      const resultParse = countrySchema.safeParse(mapped)

      if (!resultParse.success) {
        this.logger.error(resultParse.error)

        return acc
      }

      return [...acc, mapped]
    }, [] as Country[])
  }

  private async fetchOnrampData(): Promise<Offer[]> {
    const offers = await this.firebase.getAllDocuments<OnrampData>('onrampData')

    if (!offers) throw new Error('Offers not found')

    return offers.reduce((acc, data) => {
      const responseParse = onrampDataSchema.safeParse(data)

      if (!responseParse.success) {
        this.logger.error(`Cannot parse offer ${JSON.stringify(data)}`)

        return acc
      }

      const fiatCurrency = data.currency
      const tokenId = data.coinId
      const { providerName } = data
      const id = `${providerName}-${tokenId}-${fiatCurrency}`

      const offer: Offer = {
        id,
        fiatCurrency,
        tokenId,
        providerName,
        chainId: data.chainId,
        paymentMethods: this.mapPaymentMethods(data.paymentMethods),
        providerLogoUrl: data.providerLogoUrl,
        providerFees: data.providerFees,
        onrampUrl: data.onrampUrl,
        offrampUrl: data.offrampUrl,
      }

      const offerParse = offerSchema.safeParse(offer)

      if (!offerParse.success) {
        this.logger.error(`Cannot parse mapped offer ${JSON.stringify(offerParse.error.errors)}`)

        return acc
      }

      return [...acc, offer]
    }, [] as Offer[])
  }

  private mapPaymentMethods(paymentMethods: string[]): PaymentMethod[] {
    return paymentMethods.map((method) => method)
  }
}

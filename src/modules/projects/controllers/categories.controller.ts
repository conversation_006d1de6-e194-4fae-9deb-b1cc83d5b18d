import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, ParseIntPipe } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'

import { CookieAuthGuard } from '../../auth/guards/cookie-auth.guard'
import { CreateCategoryDto } from '../dto/create-category.dto'
import { UpdateCategoryDto } from '../dto/update-category.dto'
import { CategoriesService } from '../services/categories.service'

@ApiTags('Categories')
@Controller('categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all project categories' })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  async getAllCategories() {
    return this.categoriesService.findAll()
  }

  @Post()
  @UseGuards(CookieAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new category (Admin)' })
  @ApiResponse({ status: 201, description: 'Category created successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createCategory(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoriesService.create(createCategoryDto)
  }

  @Put(':id')
  @UseGuards(CookieAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a category (Admin)' })
  @ApiResponse({ status: 200, description: 'Category updated successfully' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateCategory(@Param('id', ParseIntPipe) categoryId: number, @Body() updateCategoryDto: UpdateCategoryDto) {
    return this.categoriesService.update(categoryId, updateCategoryDto)
  }

  @Delete(':id')
  @UseGuards(CookieAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a category (Admin)' })
  @ApiResponse({ status: 200, description: 'Category deleted successfully' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteCategory(@Param('id', ParseIntPipe) categoryId: number) {
    return this.categoriesService.delete(categoryId)
  }

  @Post(':projectId/assign/:categoryId')
  @UseGuards(CookieAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Assign category to project' })
  @ApiResponse({ status: 200, description: 'Category assigned successfully' })
  @ApiResponse({ status: 404, description: 'Project or category not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async assignCategoryToProject(
    @Param('projectId') projectId: string,
    @Param('categoryId', ParseIntPipe) categoryId: number,
  ) {
    return this.categoriesService.assignToProject(projectId, categoryId)
  }

  @Delete(':projectId/remove/:categoryId')
  @UseGuards(CookieAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove category from project' })
  @ApiResponse({ status: 200, description: 'Category removed successfully' })
  @ApiResponse({ status: 404, description: 'Project or category not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async removeCategoryFromProject(
    @Param('projectId') projectId: string,
    @Param('categoryId', ParseIntPipe) categoryId: number,
  ) {
    return this.categoriesService.removeFromProject(projectId, categoryId)
  }

  @Get('project/:projectId')
  @ApiOperation({ summary: 'Get categories for a specific project' })
  @ApiResponse({ status: 200, description: 'Project categories retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async getProjectCategories(@Param('projectId') projectId: string) {
    return this.categoriesService.getProjectCategories(projectId)
  }
}

import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Request } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'

import { CookieAuthGuard } from '../../auth/guards/cookie-auth.guard'
import { CreateAuditDto } from '../dto/create-audit.dto'
import { UpdateAuditDto } from '../dto/update-audit.dto'
import { AuditsService } from '../services/audits.service'

@ApiTags('Project Audits')
@Controller('projects/:projectId/audits')
@UseGuards(CookieAuthGuard)
@ApiBearerAuth()
export class AuditsController {
  constructor(private readonly auditsService: AuditsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all audits for a project' })
  @ApiResponse({ status: 200, description: 'Audits retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProjectAudits(@Param('projectId') projectId: string) {
    return this.auditsService.getProjectAudits(projectId)
  }

  @Post()
  @ApiOperation({ summary: 'Add an audit to a project' })
  @ApiResponse({ status: 201, description: 'Audit created successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createAudit(
    @Param('projectId') projectId: string,
    @Body() createAuditDto: CreateAuditDto,
    @Request() req: any,
  ) {
    const userId = req.user.id

    return this.auditsService.create(projectId, createAuditDto, userId)
  }

  @Put(':auditId')
  @ApiOperation({ summary: 'Update an audit' })
  @ApiResponse({ status: 200, description: 'Audit updated successfully' })
  @ApiResponse({ status: 404, description: 'Audit not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateAudit(
    @Param('projectId') projectId: string,
    @Param('auditId') auditId: string,
    @Body() updateAuditDto: UpdateAuditDto,
    @Request() req: any,
  ) {
    const userId = req.user.id

    return this.auditsService.update(auditId, updateAuditDto, userId, projectId)
  }

  @Delete(':auditId')
  @ApiOperation({ summary: 'Delete an audit' })
  @ApiResponse({ status: 200, description: 'Audit deleted successfully' })
  @ApiResponse({ status: 404, description: 'Audit not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteAudit(@Param('projectId') projectId: string, @Param('auditId') auditId: string, @Request() req: any) {
    const userId = req.user.id

    return this.auditsService.delete(auditId, userId, projectId)
  }

  @Get(':auditId')
  @ApiOperation({ summary: 'Get a specific audit' })
  @ApiResponse({ status: 200, description: 'Audit retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Audit not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getAudit(@Param('auditId') auditId: string) {
    return this.auditsService.findById(auditId)
  }
}

import { Controller, Post, Get, UseGuards, Param, Body, Query, Delete, Put } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiHeader, ApiSecurity, ApiQuery } from '@nestjs/swagger'

import { AdminKeyGuard } from '../../auth/guards/admin-key.guard'
import { UpdateProjectDto } from '../dto/update-project.dto'
import { UpdateProjectStatusDto } from '../dto/update-status.dto'
import { AdminService } from '../services/admin.service'

@ApiTags('Admin')
@Controller('admin')
@ApiSecurity('AdminKey')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('seed-categories')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Seed initial project categories (Admin only)',
    description: `
    Seeds the database with 20 predefined blockchain project categories.
    
    **Categories seeded:**
    - DeFi, NFT, Gaming, Infrastructure, Analytics
    - DAO, Metaverse, Layer 2, Cross-chain, AI/ML
    - SocialFi, Privacy, Identity, DEX, Lending
    - Yield Farming, RWA (Real World Assets), Stablecoin, Insurance, Prediction Markets
    
    **How it works:**
    1. Attempts to insert each predefined category
    2. Uses onConflictDoNothing() to prevent duplicates
    3. Returns only newly created categories
    4. Safe to run multiple times
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 201,
    description: 'Categories seeded successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Successfully seeded 20 categories' },
        categories: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              categoryId: { type: 'number', example: 1 },
              categoryName: { type: 'string', example: 'DeFi' },
            },
          },
          example: [
            { categoryId: 1, categoryName: 'DeFi' },
            { categoryId: 2, categoryName: 'NFT' },
            { categoryId: 3, categoryName: 'Gaming' },
          ],
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing admin key',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Admin key is required. Please provide X-Admin-Key header.' },
        error: { type: 'string', example: 'Unauthorized' },
        statusCode: { type: 'number', example: 401 },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access not configured',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Admin access is not configured on this server' },
        error: { type: 'string', example: 'Forbidden' },
        statusCode: { type: 'number', example: 403 },
      },
    },
  })
  async seedCategories() {
    return this.adminService.seedCategories()
  }

  @Get('stats')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Get platform statistics (Admin only)',
    description: `
    Retrieves comprehensive platform analytics and statistics.
    
    **Includes:**
    - Total counts (users, projects, categories, team members, audits)
    - Project status distribution (draft, submitted, approved, etc.)
    - Blockchain distribution (ethereum, solana, polygon, etc.)
    - Generated timestamp
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        overview: {
          type: 'object',
          properties: {
            totalUsers: { type: 'string', example: '157' },
            totalProjects: { type: 'string', example: '89' },
            totalCategories: { type: 'string', example: '20' },
            totalTeamMembers: { type: 'string', example: '342' },
            totalAudits: { type: 'string', example: '67' },
          },
        },
        distributions: {
          type: 'object',
          properties: {
            projectStatus: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  status: { type: 'string', example: 'submitted' },
                  count: { type: 'string', example: '45' },
                },
              },
              example: [
                { status: 'draft', count: '34' },
                { status: 'submitted', count: '45' },
                { status: 'approved', count: '10' },
              ],
            },
            blockchains: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  blockchain: { type: 'string', example: 'ethereum' },
                  count: { type: 'string', example: '67' },
                },
              },
              example: [
                { blockchain: 'ethereum', count: '67' },
                { blockchain: 'solana', count: '22' },
              ],
            },
          },
        },
        generatedAt: { type: 'string', example: '2025-06-09T21:15:00.000Z' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing admin key',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Invalid admin key' },
        error: { type: 'string', example: 'Unauthorized' },
        statusCode: { type: 'number', example: 401 },
      },
    },
  })
  async getStats() {
    return this.adminService.getPlatformStats()
  }

  @Get('health-check')
  @ApiOperation({
    summary: 'Admin health check endpoint',
    description: `
    System health monitoring endpoint for admin dashboard.
    
    **No authentication required** - Public endpoint for monitoring.
    
    **Returns:**
    - System status (healthy/unhealthy)
    - Current timestamp
    - Service status (database, authentication, api)
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'All systems operational',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', example: '2025-06-09T21:15:00.000Z' },
        services: {
          type: 'object',
          properties: {
            database: { type: 'string', example: 'connected' },
            authentication: { type: 'string', example: 'active' },
            api: { type: 'string', example: 'running' },
          },
        },
      },
    },
  })
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        authentication: 'active',
        api: 'running',
      },
    }
  }

  @Get('users')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  @ApiOperation({
    summary: 'Get all users (Admin only)',
    description: `
    Retrieves all registered users with pagination.
    
    **Features:**
    - Pagination support
    - User verification status
    - Registration dates
    - Contact information
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        users: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'user-123' },
              email: { type: 'string', example: '<EMAIL>' },
              name: { type: 'string', example: 'John Doe' },
              phoneNumber: { type: 'string', example: '+1234567890' },
              emailVerified: { type: 'boolean', example: true },
              phoneVerified: { type: 'boolean', example: true },
              createdAt: { type: 'string', example: '2025-06-10T10:00:00Z' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 20 },
            total: { type: 'number', example: 157 },
            pages: { type: 'number', example: 8 },
          },
        },
      },
    },
  })
  async getAllUsers(@Query('page') page?: number, @Query('limit') limit?: number) {
    return this.adminService.getAllUsers({ page: page || 1, limit: limit || 20 })
  }

  @Get('projects')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'blockchain', required: false, description: 'Filter by blockchain' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  @ApiOperation({
    summary: 'Get all projects (Admin only)',
    description: `
    Retrieves all projects with filtering and pagination.
    
    **Filters:**
    - Status: draft, submitted, approved, rejected
    - Blockchain: ethereum, solana, polygon, etc.
    
    **Features:**
    - Complete project details
    - User information
    - Submission dates
    - Categories and audits count
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Projects retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        projects: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              projectId: { type: 'string', example: 'proj-123' },
              projectName: { type: 'string', example: 'DeFi Revolution' },
              submissionStatus: { type: 'string', example: 'submitted' },
              blockchainToLaunchOn: { type: 'string', example: 'ethereum' },
              contactEmail: { type: 'string', example: '<EMAIL>' },
              currentFormStep: { type: 'number', example: 11 },
              createdAt: { type: 'string', example: '2025-06-10T10:00:00Z' },
              updatedAt: { type: 'string', example: '2025-06-10T11:00:00Z' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 20 },
            total: { type: 'number', example: 89 },
            pages: { type: 'number', example: 5 },
          },
        },
      },
    },
  })
  async getAllProjects(
    @Query('status') status?: string,
    @Query('blockchain') blockchain?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.adminService.getAllProjects({
      status,
      blockchain,
      page: page || 1,
      limit: limit || 20,
    })
  }

  @Put('projects/:projectId/status')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Update project status (Admin only)',
    description: `
    Updates the status of a project submission.
    
    **Available Statuses:**
    - draft: Project in progress
    - submitted: Awaiting review
    - approved: Project approved
    - rejected: Project rejected
    - under_review: Currently being reviewed
    - wallet_provided: Wallet address provided
    - fee_paid: Fee paid
    - liquidity_provided: Liquidity provided
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Project status updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Project status updated to approved' },
        project: {
          type: 'object',
          properties: {
            projectId: { type: 'string', example: 'proj-123' },
            projectName: { type: 'string', example: 'DeFi Revolution' },
            submissionStatus: { type: 'string', example: 'approved' },
            updatedAt: { type: 'string', example: '2025-06-10T11:00:00Z' },
          },
        },
      },
    },
  })
  async updateProjectStatus(@Param('projectId') projectId: string, @Body() updateStatusDto: UpdateProjectStatusDto) {
    return this.adminService.updateProjectStatus(projectId, updateStatusDto.status, updateStatusDto.comment)
  }

  @Get('projects/:projectId')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Get project details by ID (Admin only)',
    description: `
    Retrieves complete project details by project ID for editing.
    
    **Includes all project data:**
    - Basic information (name, website, contact)
    - Project details and team information
    - Fundraising and community data
    - Token and TGE details
    - Marketing and banner information
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Project details retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        project: {
          type: 'object',
          description: 'Complete project data',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Project with ID proj-123 not found' },
        error: { type: 'string', example: 'Not Found' },
        statusCode: { type: 'number', example: 404 },
      },
    },
  })
  async getProjectById(@Param('projectId') projectId: string) {
    return this.adminService.getProjectById(projectId)
  }

  @Put('projects/:projectId')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Update project details (Admin only)',
    description: `
    Updates project details with provided fields. Only provided fields will be updated.
    
    **Editable Fields:**
    - Basic Information: name, website, contact details, blockchain
    - Launch Type: launch type, curation status, partner name
    - Project Content: project details, team introduction, fundraising history
    - Social Media: Twitter, Discord, Telegram, Farcaster
    - Token Information: name, symbol, contract address, supply, market cap
    - TGE Details: type, allocations, pricing, dates, trading limits
    - Marketing: strategy content, banner images, header description
    - Post-Approval: wallet address
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Project updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Project updated successfully' },
        project: {
          type: 'object',
          description: 'Updated project data',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Project with ID proj-123 not found' },
        error: { type: 'string', example: 'Not Found' },
        statusCode: { type: 'number', example: 404 },
      },
    },
  })
  async updateProject(@Param('projectId') projectId: string, @Body() updateProjectDto: UpdateProjectDto) {
    return this.adminService.updateProject(projectId, updateProjectDto)
  }

  @Delete('projects/:projectId')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Delete project (Admin only)',
    description: `
    Permanently deletes a project and all related data.
    
    **Cascading Deletes:**
    - Project categories assignments
    - Team members
    - Project audits
    - All form data
    
    **Warning:** This action cannot be undone.
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Project deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Project and all related data deleted successfully' },
        deletedProjectId: { type: 'string', example: 'proj-123' },
      },
    },
  })
  async deleteProject(@Param('projectId') projectId: string) {
    return this.adminService.deleteProject(projectId)
  }

  @Post('reset-database')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Reset database (Admin only - DANGEROUS)',
    description: `
    **⚠️ DANGER ZONE ⚠️**
    
    Completely resets the database to initial state.
    
    **What gets deleted:**
    - All projects and form data
    - All team members
    - All project audits
    - All category assignments
    - All users (except system admin)
    
    **What remains:**
    - Categories table (seeded categories)
    - System configuration
    
    **Use case:** Development/testing environment reset
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Database reset successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Database reset completed successfully' },
        resetAt: { type: 'string', example: '2025-06-10T11:00:00Z' },
        stats: {
          type: 'object',
          properties: {
            deletedProjects: { type: 'number', example: 45 },
            deletedUsers: { type: 'number', example: 123 },
            deletedTeamMembers: { type: 'number', example: 200 },
            deletedAudits: { type: 'number', example: 67 },
          },
        },
      },
    },
  })
  async resetDatabase() {
    return this.adminService.resetDatabase()
  }

  @Get('export-data')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiQuery({ name: 'format', required: false, description: 'Export format', enum: ['json', 'csv'] })
  @ApiOperation({
    summary: 'Export platform data (Admin only)',
    description: `
    Exports all platform data for backup or analysis.
    
    **Export Formats:**
    - json: Complete data with relationships
    - csv: Flat tables for spreadsheet analysis
    
    **Included Data:**
    - All users and verification status
    - All projects with complete form data
    - Team members and social profiles
    - Project audits and reports
    - Categories and assignments
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Data exported successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        exportedAt: { type: 'string', example: '2025-06-10T11:00:00Z' },
        format: { type: 'string', example: 'json' },
        data: {
          type: 'object',
          properties: {
            users: { type: 'array' },
            projects: { type: 'array' },
            teamMembers: { type: 'array' },
            audits: { type: 'array' },
            categories: { type: 'array' },
          },
        },
        stats: {
          type: 'object',
          properties: {
            totalUsers: { type: 'number', example: 157 },
            totalProjects: { type: 'number', example: 89 },
            exportSize: { type: 'string', example: '2.5MB' },
          },
        },
      },
    },
  })
  async exportData(@Query('format') format?: string) {
    return this.adminService.exportPlatformData(format || 'json')
  }
}

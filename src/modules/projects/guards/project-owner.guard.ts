import { Injectable, CanActivate, ExecutionContext, ForbiddenException, NotFoundException } from '@nestjs/common'
import { Reflector } from '@nestjs/core'

import { ProjectsService } from '../projects.service'

@Injectable()
export class ProjectOwnerGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private projectsService: ProjectsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const { user } = request
    const projectId = request.params.id || request.params.projectId

    if (!user) {
      throw new ForbiddenException('User not authenticated')
    }

    if (!projectId) {
      // If no project ID in params, allow access (for creating new projects)
      return true
    }

    try {
      const project = await this.projectsService.findByIdAndUserId(projectId, user.id)

      if (!project) {
        throw new NotFoundException('Project not found or access denied')
      }

      return true
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error
      }

      throw new ForbiddenException('Access denied')
    }
  }
}

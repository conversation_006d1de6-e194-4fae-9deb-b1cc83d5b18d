import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const submitWalletSchema = z.object({
  walletAddress: z
    .string()
    .min(1, 'Wallet address is required')
    .max(255)
    .refine(
      (address) => {
        // EVM addresses (Ethereum, Polygon, BSC, Arbitrum, Base, Optimism)
        const evmRegex = /^(0x)[0-9A-Fa-f]{40}$/
        // Solana addresses
        const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/

        return evmRegex.test(address) || solanaRegex.test(address)
      },
      {
        message: 'Invalid wallet address format. Must be a valid EVM address (0x...) or Solana address',
      },
    ),
})

export class SubmitWalletDto extends createZodDto(submitWalletSchema) {
  @ApiProperty({
    example: '******************************************',
    description: 'Wallet address for token launch. Must be a valid EVM address (0x...) or Solana address',
    pattern: '^(0x)[0-9A-Fa-f]{40}$|^[1-9A-HJ-NP-Za-km-z]{32,44}$',
  })
  walletAddress!: string
}

import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const createProjectSchema = z.object({
  projectName: z.string().min(1, 'Project name is required').max(255),
  projectWebsite: z.string().url('Invalid website URL').max(2048),
  contactName: z.string().min(1, 'Contact name is required').max(255),
  contactEmail: z.string().email('Invalid email address').max(255),
  roleInProject: z.string().min(1, 'Role is required').max(255),
  blockchainToLaunchOn: z.enum(['ethereum', 'solana', 'polygon', 'bsc', 'arbitrum', 'base', 'optimism']),
  teamPublicProfileLinks: z.string().optional(),
  launchType: z.enum(['fair_launch', 'curated']).default('fair_launch'),
  partnerName: z.string().max(255).optional(),
})

export class CreateProjectDto extends createZodDto(createProjectSchema) {
  @ApiProperty({
    example: 'DeFi Protocol',
    description: 'Name of the project',
  })
  projectName!: string

  @ApiProperty({
    example: 'https://example.com',
    description: 'Website URL of the project',
  })
  projectWebsite!: string

  @ApiProperty({
    example: 'John Doe',
    description: 'Primary contact person name',
  })
  contactName!: string

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Primary contact email address',
  })
  contactEmail!: string

  @ApiProperty({
    example: 'Founder',
    description: 'Role of the contact person in the project',
  })
  roleInProject!: string

  @ApiProperty({
    example: 'ethereum',
    enum: ['ethereum', 'solana', 'polygon', 'bsc', 'arbitrum', 'base', 'optimism'],
    description: 'Blockchain platform to launch on',
  })
  blockchainToLaunchOn!: 'ethereum' | 'solana' | 'polygon' | 'bsc' | 'arbitrum' | 'base' | 'optimism'

  @ApiProperty({
    example: 'https://linkedin.com/in/founder, https://github.com/cofounder',
    description: 'Comma-separated team member public profile links',
    required: false,
  })
  teamPublicProfileLinks?: string

  @ApiProperty({
    example: 'fair_launch',
    enum: ['fair_launch', 'curated'],
    description: 'Launch type - fair launch or curated partnership',
    default: 'fair_launch',
  })
  launchType!: 'fair_launch' | 'curated'

  @ApiProperty({
    example: 'Partner Name',
    description: 'Name of the partner for curated launches (required only for curated type)',
    required: false,
  })
  partnerName?: string
}

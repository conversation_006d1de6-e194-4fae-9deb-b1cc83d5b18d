import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const updateTeamMemberSchema = z.object({
  memberName: z.string().min(1, 'Member name is required').max(255).optional(),
  memberRole: z.string().min(1, 'Member role is required').max(255).optional(),
  memberBio: z.string().optional(),
  memberLinkedin: z.string().url().optional(),
  memberTwitter: z.string().url().optional(),
  memberProfileImageUrl: z.string().url().optional(),
})

export class UpdateTeamMemberDto extends createZodDto(updateTeamMemberSchema) {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Name of the team member',
    maxLength: 255,
    required: false,
  })
  memberName?: string

  @ApiProperty({
    example: 'CTO',
    description: 'Role of the team member',
    maxLength: 255,
    required: false,
  })
  memberRole?: string

  @ApiProperty({
    example: 'Experienced blockchain developer with 5+ years in DeFi',
    description: 'Biography of the team member',
    required: false,
  })
  memberBio?: string

  @ApiProperty({
    example: 'https://linkedin.com/in/johndoe',
    description: 'LinkedIn profile URL',
    required: false,
  })
  memberLinkedin?: string

  @ApiProperty({
    example: 'https://twitter.com/johndoe',
    description: 'Twitter profile URL',
    required: false,
  })
  memberTwitter?: string

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: 'Profile image URL',
    required: false,
  })
  memberProfileImageUrl?: string
}

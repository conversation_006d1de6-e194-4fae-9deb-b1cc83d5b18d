import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const createCategorySchema = z.object({
  categoryName: z.string().min(1, 'Category name is required').max(100),
})

export class CreateCategoryDto extends createZodDto(createCategorySchema) {
  @ApiProperty({
    example: 'DeFi',
    description: 'Name of the category',
    maxLength: 100,
  })
  categoryName!: string
}

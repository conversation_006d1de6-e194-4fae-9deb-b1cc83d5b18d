import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const updateAuditSchema = z.object({
  auditDate: z.string().optional(),
  auditingFirm: z.string().optional(),
  auditReportUrl: z.string().url().optional(),
  auditingFirmLogoUrl: z.string().url().optional(),
})

export class UpdateAuditDto extends createZodDto(updateAuditSchema) {
  @ApiProperty({
    example: '2025-06-01',
    description: 'Date when the audit was conducted',
    required: false,
  })
  auditDate?: string

  @ApiProperty({
    example: 'Hacken',
    description: 'Name of the auditing firm',
    required: false,
  })
  auditingFirm?: string

  @ApiProperty({
    example: 'https://example.com/audit-report.pdf',
    description: 'URL to the audit report',
    required: false,
  })
  auditReportUrl?: string

  @ApiProperty({
    example: 'https://example.com/hacken-logo.png',
    description: 'URL to the auditing firm logo',
    required: false,
  })
  auditingFirmLogoUrl?: string
}

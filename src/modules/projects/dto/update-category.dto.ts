import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const updateCategorySchema = z.object({
  categoryName: z.string().min(1, 'Category name is required').max(100),
})

export class UpdateCategoryDto extends createZodDto(updateCategorySchema) {
  @ApiProperty({
    example: 'DeFi Protocol',
    description: 'Updated name of the category',
    maxLength: 100,
  })
  categoryName!: string
}

import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

import { submissionStatusEnum } from '../../../libs/database/schema'

// Create a Zod enum from the database schema
const statusEnum = z.enum(submissionStatusEnum.enumValues as [string, ...string[]])

const updateStatusSchema = z.object({
  status: statusEnum,
  comment: z.string().optional(),
})

// Infer the submission status type from the database schema
type SubmissionStatus = (typeof submissionStatusEnum.enumValues)[number]

export class UpdateProjectStatusDto extends createZodDto(updateStatusSchema) {
  @ApiProperty({
    example: 'under_review',
    enum: submissionStatusEnum.enumValues,
    description: 'New status for the project',
  })
  status!: SubmissionStatus

  @ApiProperty({
    example: 'Needs more information about security audits',
    description: 'Optional comment for status change',
    required: false,
  })
  comment?: string
}

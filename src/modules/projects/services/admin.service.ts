import { Injectable, Inject, Logger, NotFoundException } from '@nestjs/common'
import { sql, eq, and, desc } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { LaunchpadTokenService } from './launchpad-token.service'
import { ProjectNotificationService } from './project-notification.service'
import { ProjectStatusService } from './project-status.service'
import {
  projectCategories,
  projects,
  projectAudits,
  users,
  projectHasCategories,
  submissionStatusEnum,
} from '../../../libs/database/schema'

// Infer the submission status type from the database schema
type SubmissionStatus = (typeof submissionStatusEnum.enumValues)[number]

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name)

  constructor(
    @Inject('DB') private db: NodePgDatabase<any>,
    private projectStatusService: ProjectStatusService,
    private launchpadTokenService: LaunchpadTokenService,
    private projectNotificationService: ProjectNotificationService,
  ) {}

  async seedCategories() {
    const categoriesData = [
      'DeFi (Decentralized Finance)',
      'NFTs (Non-Fungible Tokens)',
      'Gaming / GameFi',
      'Infrastructure / L1 / L2',
      'DePIN (Decentralized Physical Infrastructure)',
      'Consumer / Web3 Social',
      'DAOs & Governance',
      'RWA (Real World Assets)',
      'Oracles',
      'Privacy & Zero-Knowledge (ZK)',
      'AI Infrastructure',
      'AI Framework',
      'Security / Auditing / Compliance',
      'Identity / Reputation',
      'Bridges & Cross-Chain Protocols',
      'Interoperability / Chain Abstraction',
      'MEV / Infrastructure',
      'Liquid Staking / LSDs / LRTs',
      'Payments & On/Off Ramps',
      'Perpetuals / DEXs',
      'Yield Aggregators',
      'Stablecoins & Liquidity Protocols',
      'Wallets & Key Management',
      'Developer Tooling & SDKs',
      'Data & Analytics',
      'Restaking / Actively Validated Services (AVS)',
      'SocialFi',
      'Crypto Messaging / Comms Infra',
      'Creator Economy & Fan Tokens',
      'Prediction Markets',
      'Modular Chains',
      'Decentralized AI Agents',
      'Onchain Credentials / Proof-of-Personhood',
      'Tokenized Communities',
      'NFT Infra / NFT-Fi',
      'ZK Identity / Private Credentials',
      'Regenerative Finance (ReFi)',
    ]

    const results = []
    for (const categoryName of categoriesData) {
      try {
        const [category] = await this.db
          .insert(projectCategories)
          .values({ categoryName })
          .onConflictDoNothing()
          .returning()

        if (category) {
          results.push(category)
        }
      } catch {
        this.logger.warn(`Category '${categoryName}' might already exist`)
      }
    }

    this.logger.log(`Seeded ${results.length} categories`)

    return {
      success: true,
      message: `Successfully seeded ${results.length} categories`,
      categories: results,
    }
  }

  async getPlatformStats() {
    const [userCount] = await this.db.select({ count: sql<number>`count(*)` }).from(users)
    const [projectCount] = await this.db.select({ count: sql<number>`count(*)` }).from(projects)
    const [categoryCount] = await this.db.select({ count: sql<number>`count(*)` }).from(projectCategories)
    const [auditCount] = await this.db.select({ count: sql<number>`count(*)` }).from(projectAudits)

    // Get project status distribution
    const statusDistribution = await this.db
      .select({
        status: projects.submissionStatus,
        count: sql<number>`count(*)`,
      })
      .from(projects)
      .groupBy(projects.submissionStatus)

    // Get blockchain distribution
    const blockchainDistribution = await this.db
      .select({
        blockchain: projects.blockchainToLaunchOn,
        count: sql<number>`count(*)`,
      })
      .from(projects)
      .groupBy(projects.blockchainToLaunchOn)

    return {
      overview: {
        totalUsers: userCount.count,
        totalProjects: projectCount.count,
        totalCategories: categoryCount.count,
        totalAudits: auditCount.count,
      },
      distributions: {
        projectStatus: statusDistribution,
        blockchains: blockchainDistribution,
      },
      generatedAt: new Date().toISOString(),
    }
  }

  async getAllUsers(options: { page: number; limit: number }) {
    const { page, limit } = options
    const offset = (page - 1) * limit

    // Get total count
    const [{ count: totalUsers }] = await this.db.select({ count: sql<number>`count(*)` }).from(users)

    // Get paginated users
    const usersList = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        phoneNumber: users.phoneNumber,
        emailVerified: users.emailVerified,
        phoneVerified: users.phoneVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .orderBy(desc(users.createdAt))
      .limit(limit)
      .offset(offset)

    return {
      users: usersList,
      pagination: {
        page,
        limit,
        total: totalUsers,
        pages: Math.ceil(totalUsers / limit),
      },
    }
  }

  async getAllProjects(options: { status?: string; blockchain?: string; page: number; limit: number }) {
    const { status, blockchain, page, limit } = options
    const offset = (page - 1) * limit

    // Build where conditions
    const conditions = []

    if (status) {
      conditions.push(sql`${projects.submissionStatus} = ${status}`)
    }

    if (blockchain) {
      conditions.push(sql`${projects.blockchainToLaunchOn} = ${blockchain}`)
    }

    // Get total count with filters
    let countQuery = this.db.select({ count: sql<number>`count(*)` }).from(projects)

    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions)) as any
    }

    const [{ count: totalProjects }] = await countQuery

    // Get paginated projects with filters
    let projectsQuery = this.db
      .select({
        projectId: projects.projectId,
        projectName: projects.projectName,
        submissionStatus: projects.submissionStatus,
        blockchainToLaunchOn: projects.blockchainToLaunchOn,
        contactEmail: projects.contactEmail,
        contactName: projects.contactName,
        currentFormStep: projects.currentFormStep,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        userId: projects.userId,
      })
      .from(projects)
      .orderBy(desc(projects.updatedAt))
      .limit(limit)
      .offset(offset)

    if (conditions.length > 0) {
      projectsQuery = projectsQuery.where(and(...conditions)) as any
    }

    const projectsList = await projectsQuery

    return {
      projects: projectsList,
      pagination: {
        page,
        limit,
        total: totalProjects,
        pages: Math.ceil(totalProjects / limit),
      },
    }
  }

  async updateProjectStatus(projectId: string, status: SubmissionStatus, comment?: string) {
    // Check if project exists first
    const projectResult = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (projectResult.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    // Use the centralized ProjectStatusService for proper validation and history tracking
    await this.projectStatusService.updateProjectStatus(projectId, status, 'admin', comment)

    // Get the updated project for response
    const updatedProject = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    this.logger.log(`Project ${projectId} status updated to ${status} by admin`)

    // Send approval email when project is approved
    if (status === 'approved') {
      try {
        // Get user information for the project
        const userResult = await this.db
          .select({
            userId: projects.userId,
            userEmail: users.email,
            userName: users.name,
          })
          .from(projects)
          .innerJoin(users, eq(projects.userId, users.id))
          .where(eq(projects.projectId, projectId))
          .limit(1)

        if (userResult.length > 0) {
          const user = userResult[0]
          const project = updatedProject[0]

          await this.projectNotificationService.sendProjectApprovalEmail(
            {
              projectId: project.projectId,
              projectName: project.projectName,
              launchType: project.launchType,
              partnerName: project.partnerName,
            },
            {
              id: user.userId,
              email: user.userEmail,
              name: user.userName,
            },
          )

          this.logger.log(`Sent approval email to ${user.userEmail} for project ${projectId}`)
        } else {
          this.logger.warn(`Could not find user information for project ${projectId}`)
        }
      } catch (error) {
        this.logger.error(`Failed to send approval email for project ${projectId}:`, error)
        // Don't throw - status update should succeed even if email fails
      }
    }

    if (status === 'liquidity_provided') {
      try {
        this.logger.log(`Triggering token creation for project ${projectId}`)
        const token = await this.launchpadTokenService.createLaunchpadTokenFromProject(projectId)

        if (token) {
          this.logger.log(`Successfully created token ${token.id} for project ${projectId}`)
        } else {
          this.logger.warn(`Token creation returned null for project ${projectId}`)
        }
      } catch (error) {
        this.logger.error(`Failed to create token for project ${projectId}:`, error)
        // Don't throw - status update should succeed even if token creation fails
      }
    }

    return {
      success: true,
      message: `Project status updated to ${status}`,
      project: {
        projectId: updatedProject[0].projectId,
        projectName: updatedProject[0].projectName,
        submissionStatus: updatedProject[0].submissionStatus,
        updatedAt: updatedProject[0].updatedAt,
      },
      comment,
    }
  }

  async deleteProject(projectId: string) {
    const projectResult = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (projectResult.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    // Delete in correct order (foreign key constraints)
    // 1. Delete project category assignments
    await this.db.delete(projectHasCategories).where(eq(projectHasCategories.projectId, projectId))

    // 2. Delete project audits
    await this.db.delete(projectAudits).where(eq(projectAudits.projectId, projectId))

    // 3. Delete the project itself
    await this.db.delete(projects).where(eq(projects.projectId, projectId))

    this.logger.log(`Project ${projectId} and all related data deleted`)

    return {
      success: true,
      message: 'Project and all related data deleted successfully',
      deletedProjectId: projectId,
    }
  }

  async resetDatabase() {
    const resetAt = new Date()

    // Get counts before deletion
    const [{ count: projectCount }] = await this.db.select({ count: sql<number>`count(*)` }).from(projects)
    const [{ count: userCount }] = await this.db.select({ count: sql<number>`count(*)` }).from(users)
    const [{ count: auditCount }] = await this.db.select({ count: sql<number>`count(*)` }).from(projectAudits)

    // Delete data in correct order (respecting foreign keys)
    await this.db.delete(projectHasCategories)
    await this.db.delete(projectAudits)
    await this.db.delete(projects)
    await this.db.delete(users)

    this.logger.log('Database reset completed')

    return {
      success: true,
      message: 'Database reset completed successfully',
      resetAt: resetAt.toISOString(),
      stats: {
        deletedProjects: projectCount,
        deletedUsers: userCount,
        deletedAudits: auditCount,
      },
    }
  }

  async updateProject(projectId: string, updateData: any) {
    // Check if project exists first
    const projectResult = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (projectResult.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    // Filter out undefined values and prepare update data
    const filteredUpdateData = Object.fromEntries(Object.entries(updateData).filter(([, value]) => value !== undefined))

    // Add updated timestamp
    filteredUpdateData.updatedAt = new Date()

    // Update the project
    await this.db.update(projects).set(filteredUpdateData).where(eq(projects.projectId, projectId))

    // Get the updated project for response
    const updatedProject = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    this.logger.log(`Project ${projectId} updated by admin`)

    return {
      success: true,
      message: 'Project updated successfully',
      project: updatedProject[0],
    }
  }

  async getProjectById(projectId: string) {
    const projectResult = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (projectResult.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    return {
      success: true,
      project: projectResult[0],
    }
  }

  async exportPlatformData(format: string) {
    const exportedAt = new Date()

    // Get all data
    const allUsers = await this.db.select().from(users)
    const allProjects = await this.db.select().from(projects)
    const allAudits = await this.db.select().from(projectAudits)
    const allCategories = await this.db.select().from(projectCategories)
    const allCategoryAssignments = await this.db.select().from(projectHasCategories)

    const data = {
      users: allUsers,
      projects: allProjects,
      audits: allAudits,
      categories: allCategories,
      categoryAssignments: allCategoryAssignments,
    }

    // Calculate approximate size
    const dataString = JSON.stringify(data)
    const sizeInBytes = new TextEncoder().encode(dataString).length
    const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2)

    return {
      success: true,
      exportedAt: exportedAt.toISOString(),
      format,
      data,
      stats: {
        totalUsers: allUsers.length,
        totalProjects: allProjects.length,
        totalAudits: allAudits.length,
        exportSize: `${sizeInMB}MB`,
      },
    }
  }
}

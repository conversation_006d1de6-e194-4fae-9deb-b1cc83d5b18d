import { Injectable, Inject, Logger, NotFoundException, BadRequestException } from '@nestjs/common'
import { eq, and } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { projectCategories, projectHasCategories, projects } from '../../../libs/database/schema'
import { CreateCategoryDto } from '../dto/create-category.dto'
import { UpdateCategoryDto } from '../dto/update-category.dto'

@Injectable()
export class CategoriesService {
  private readonly logger = new Logger(CategoriesService.name)

  constructor(@Inject('DB') private db: NodePgDatabase<any>) {}

  async findAll() {
    const categories = await this.db.select().from(projectCategories).orderBy(projectCategories.categoryName)

    return categories
  }

  async create(createCategoryDto: CreateCategoryDto) {
    try {
      const newCategory = await this.db
        .insert(projectCategories)
        .values({
          categoryName: createCategoryDto.categoryName,
        })
        .returning()

      this.logger.log(`Created category: ${createCategoryDto.categoryName}`)

      return newCategory[0]
    } catch (error: any) {
      if (error.code === '23505') {
        // Unique constraint violation
        throw new BadRequestException(`Category '${createCategoryDto.categoryName}' already exists`)
      }

      throw error
    }
  }

  async update(categoryId: number, updateCategoryDto: UpdateCategoryDto) {
    const existingCategory = await this.db
      .select()
      .from(projectCategories)
      .where(eq(projectCategories.categoryId, categoryId))
      .limit(1)

    if (existingCategory.length === 0) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`)
    }

    try {
      const updatedCategory = await this.db
        .update(projectCategories)
        .set({
          categoryName: updateCategoryDto.categoryName,
        })
        .where(eq(projectCategories.categoryId, categoryId))
        .returning()

      this.logger.log(`Updated category ${categoryId}: ${updateCategoryDto.categoryName}`)

      return updatedCategory[0]
    } catch (error: any) {
      if (error.code === '23505') {
        throw new BadRequestException(`Category '${updateCategoryDto.categoryName}' already exists`)
      }

      throw error
    }
  }

  async delete(categoryId: number) {
    const existingCategory = await this.db
      .select()
      .from(projectCategories)
      .where(eq(projectCategories.categoryId, categoryId))
      .limit(1)

    if (existingCategory.length === 0) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`)
    }

    // First remove all project associations
    await this.db.delete(projectHasCategories).where(eq(projectHasCategories.categoryId, categoryId))

    // Then delete the category
    await this.db.delete(projectCategories).where(eq(projectCategories.categoryId, categoryId))

    this.logger.log(`Deleted category ${categoryId}: ${existingCategory[0].categoryName}`)

    return { success: true, message: `Category '${existingCategory[0].categoryName}' deleted successfully` }
  }

  async assignToProject(projectId: string, categoryId: number) {
    // Verify project exists
    const project = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (project.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    // Verify category exists
    const category = await this.db
      .select()
      .from(projectCategories)
      .where(eq(projectCategories.categoryId, categoryId))
      .limit(1)

    if (category.length === 0) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`)
    }

    // Check if already assigned
    const existing = await this.db
      .select()
      .from(projectHasCategories)
      .where(and(eq(projectHasCategories.projectId, projectId), eq(projectHasCategories.categoryId, categoryId)))
      .limit(1)

    if (existing.length > 0) {
      throw new BadRequestException(`Category '${category[0].categoryName}' is already assigned to this project`)
    }

    await this.db.insert(projectHasCategories).values({
      projectId,
      categoryId,
    })

    this.logger.log(`Assigned category ${categoryId} to project ${projectId}`)

    return {
      success: true,
      message: `Category '${category[0].categoryName}' assigned to project successfully`,
    }
  }

  async removeFromProject(projectId: string, categoryId: number) {
    const result = await this.db
      .delete(projectHasCategories)
      .where(and(eq(projectHasCategories.projectId, projectId), eq(projectHasCategories.categoryId, categoryId)))
      .returning()

    if (result.length === 0) {
      throw new NotFoundException(`Category assignment not found for project ${projectId} and category ${categoryId}`)
    }

    this.logger.log(`Removed category ${categoryId} from project ${projectId}`)

    return { success: true, message: 'Category removed from project successfully' }
  }

  async getProjectCategories(projectId: string) {
    const categoriesResult = await this.db
      .select({
        categoryId: projectCategories.categoryId,
        categoryName: projectCategories.categoryName,
      })
      .from(projectHasCategories)
      .leftJoin(projectCategories, eq(projectHasCategories.categoryId, projectCategories.categoryId))
      .where(eq(projectHasCategories.projectId, projectId))

    return categoriesResult
  }
}

import { readFileSync, existsSync } from 'fs'
import { join, resolve } from 'path'

import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '../../../config/env'
import { EmailTransportService, type EmailSenderConfig } from '../../notifications/channels/email'

// Define proper interfaces for type safety
interface ProjectData {
  projectId: string
  projectName: string
  partnerName?: string | null // Updated to match database schema (nullable)
  contactName?: string
  contactEmail?: string
  expectedTgeLaunchDate?: string
  tokenSymbol?: string
  // Add new fields
  launchType?: 'fair_launch' | 'curated'
  isCurated?: boolean
}

interface UserData {
  email: string
  id: string
  name?: string
}

@Injectable()
export class ProjectNotificationService {
  private readonly logger = new Logger(ProjectNotificationService.name)
  private readonly senderConfig: EmailSenderConfig
  private readonly templatesPath: string

  constructor(private emailTransport: EmailTransportService) {
    const { mailCoach, templatePath } = getEnvConfig()
    this.senderConfig = {
      senderName: mailCoach.senderName,
      senderEmail: mailCoach.senderEmail,
    }

    // Use configured template path if provided, otherwise auto-detect
    if (templatePath) {
      this.templatesPath = resolve(templatePath)
      this.logger.log(`Using configured template path: ${this.templatesPath}`)
    } else {
      // Robust template path resolution for both dev and production
      const isDevelopment = process.env.NODE_ENV !== 'production'

      if (isDevelopment) {
        // In development: use source path
        this.templatesPath = resolve(process.cwd(), 'src', 'modules', 'projects', 'templates')
      } else {
        // In production: use dist path
        this.templatesPath = resolve(process.cwd(), 'dist', 'modules', 'projects', 'templates')
      }

      this.logger.log(`Auto-detected template path: ${this.templatesPath}`)
    }
  }

  async sendApplicationSubmittedToUser(userEmail: string, projectName: string): Promise<void> {
    try {
      const subject = 'Application Submitted'
      const html = this.generateUserSubmissionTemplate(projectName)

      await this.emailTransport.sendEmail(
        {
          to: userEmail,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent application submitted notification to user: ${userEmail}`)
    } catch (error) {
      this.logger.error(`Failed to send user notification to ${userEmail}:`, error)
      throw error
    }
  }

  async sendProjectSubmissionToAdmin(
    adminEmail: string,
    projectData: {
      projectName: string
      launchType: string
      partnerName: string | null
      submitterName: string
      submitterEmail: string
      submissionTime: string
    },
  ): Promise<void> {
    try {
      const subject = `${projectData.launchType} Project Submission`
      const html = this.generateAdminNotificationTemplate(projectData)

      await this.emailTransport.sendEmail(
        {
          to: adminEmail,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent ${projectData.launchType} project submission notification to admin: ${adminEmail}`)
    } catch (error) {
      this.logger.error(`Failed to send admin notification to ${adminEmail}:`, error)
      throw error
    }
  }

  async sendProjectSubmissionEmails(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      // Send confirmation to user
      await this.sendApplicationSubmittedToUser(user.email, project.projectName)

      // Send notification to admin
      const { adminNotificationEmail } = getEnvConfig()

      if (!adminNotificationEmail) {
        throw new Error('adminNotificationEmail is not set in environment config')
      }

      // Determine launch type display name
      const launchTypeDisplay = project.launchType === 'curated' ? 'Curated' : 'Fair Launch'

      await this.sendProjectSubmissionToAdmin(adminNotificationEmail, {
        projectName: project.projectName,
        launchType: launchTypeDisplay,
        partnerName: project.partnerName || null,
        submitterName: project.contactName || user.email,
        submitterEmail: user.email,
        submissionTime: new Date().toISOString(),
      })

      this.logger.log(`Sent all submission notifications for project ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send submission notifications for project ${project.projectId}:`, error)
      throw error
    }
  }

  async sendWalletSubmissionEmail(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      const subject = 'Wallet Address Submitted'
      const html = this.generateWalletSubmissionTemplate(project)

      await this.emailTransport.sendEmail(
        {
          to: user.email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent wallet submission confirmation to user: ${user.email} for project: ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send wallet submission email to ${user.email}:`, error)
      throw error
    }
  }

  async sendFeePaymentConfirmationEmail(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      const subject = 'Launch Fee Payment Confirmed'
      const html = this.generateFeePaymentConfirmationTemplate(project)

      await this.emailTransport.sendEmail(
        {
          to: user.email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent fee payment confirmation to user: ${user.email} for project: ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send fee payment confirmation email to ${user.email}:`, error)
      throw error
    }
  }

  async sendLiquidityProvidedConfirmationEmail(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      const subject = 'Liquidity Successfully Provided'
      const html = this.generateLiquidityProvidedTemplate(project)

      await this.emailTransport.sendEmail(
        {
          to: user.email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent liquidity provided confirmation to user: ${user.email} for project: ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send liquidity provided confirmation email to ${user.email}:`, error)
      throw error
    }
  }

  async sendProjectApprovalEmail(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      // Determine launch type display name
      const launchTypeDisplay = project.launchType === 'curated' ? 'Curated Launch' : 'Fair Launch'
      const subject = `${launchTypeDisplay} Approved`
      const html = this.generateProjectApprovalTemplate(project)

      await this.emailTransport.sendEmail(
        {
          to: user.email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(
        `Sent ${launchTypeDisplay.toLowerCase()} approval email to user: ${user.email} for project: ${project.projectId}`,
      )
    } catch (error) {
      this.logger.error(`Failed to send project approval email to ${user.email}:`, error)
      throw error
    }
  }

  private generateUserSubmissionTemplate(projectName: string): string {
    const templatePath = join(this.templatesPath, 'user-submission.html')

    if (!existsSync(templatePath)) {
      throw new Error(`User submission template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      return this.replaceTemplateVariables(template, {
        projectName,
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read user submission template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateAdminNotificationTemplate(projectData: {
    projectName: string
    launchType: string
    partnerName: string | null
    submitterName: string
    submitterEmail: string
    submissionTime: string
  }): string {
    const templatePath = join(this.templatesPath, 'admin-notification.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Admin notification template not found at: ${templatePath}`)
    }

    const template = readFileSync(templatePath, 'utf-8')

    // Create partner name section conditionally
    const partnerNameSection = projectData.partnerName
      ? `<p style="margin: 8px 0; color: #374151;"><strong>Caishen PRO Partner:</strong> ${projectData.partnerName}</p>`
      : ''

    const variables = {
      projectName: projectData.projectName,
      launchType: projectData.launchType,
      partnerNameSection,
      submitterName: projectData.submitterName,
      submitterEmail: projectData.submitterEmail,
      submissionTime: new Date(projectData.submissionTime).toLocaleString(),
      adminPanelUrl: this.getAdminPanelUrl(),
      currentYear: new Date().getFullYear().toString(),
    }

    return this.replaceTemplateVariables(template, variables)
  }

  private generateWalletSubmissionTemplate(project: ProjectData): string {
    const templatePath = join(this.templatesPath, 'wallet-submitted.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Wallet submission template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      // Calculate deadlines based on TGE date
      const tgeDate = new Date(project.expectedTgeLaunchDate || new Date())
      const launchFeeDeadline = new Date(tgeDate)
      launchFeeDeadline.setDate(tgeDate.getDate() - 3) // 72 hours before TGE

      const liquidityProvisioningDeadline = new Date(tgeDate)
      liquidityProvisioningDeadline.setDate(tgeDate.getDate() - 1) // 24 hours before TGE

      // Format dates in a readable format
      const formatDate = (date: Date) =>
        date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'UTC',
          timeZoneName: 'short',
        })

      return this.replaceTemplateVariables(template, {
        projectName: project.projectName,
        tokenSymbol: project.tokenSymbol || 'TOKEN',
        scheduledTgeDate: formatDate(tgeDate),
        launchFeeDeadline: formatDate(launchFeeDeadline),
        liquidityProvisioningDeadline: formatDate(liquidityProvisioningDeadline),
        payLaunchFeeUrl: this.getPayLaunchFeeUrl(project.projectId),
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read wallet submission template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateFeePaymentConfirmationTemplate(project: ProjectData): string {
    const templatePath = join(this.templatesPath, 'fee-payment-confirmed.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Fee payment confirmation template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      // Calculate deadlines based on TGE date (same as wallet submission)
      const tgeDate = new Date(project.expectedTgeLaunchDate || new Date())
      const liquidityProvisioningDeadline = new Date(tgeDate)
      liquidityProvisioningDeadline.setDate(tgeDate.getDate() - 1) // 24 hours before TGE

      // Format dates in a readable format
      const formatDate = (date: Date) =>
        date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'UTC',
          timeZoneName: 'short',
        })

      return this.replaceTemplateVariables(template, {
        projectName: project.projectName,
        tokenSymbol: project.tokenSymbol || 'TOKEN',
        scheduledTgeDate: formatDate(tgeDate),
        liquidityProvisioningDeadline: formatDate(liquidityProvisioningDeadline),
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read fee payment confirmation template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateLiquidityProvidedTemplate(project: ProjectData): string {
    const templatePath = join(this.templatesPath, 'liquidity-provided.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Liquidity provided template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      // Format TGE date for display
      const tgeDate = new Date(project.expectedTgeLaunchDate || new Date())
      const formatDate = (date: Date) =>
        `${date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'UTC',
        })} UTC`

      return this.replaceTemplateVariables(template, {
        projectName: project.projectName,
        tokenSymbol: project.tokenSymbol || 'TOKEN',
        tgeDate: formatDate(tgeDate),
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read liquidity provided template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateProjectApprovalTemplate(project: ProjectData): string {
    const templatePath = join(this.templatesPath, 'project-approved.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Project approval template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      // Determine launch type display names
      const launchTypeDisplay = project.launchType === 'curated' ? 'Curated Launch' : 'Fair Launch'
      const launchTypeLower = launchTypeDisplay.toLowerCase()

      // Create partner name section conditionally
      const partnerNameSection = project.partnerName
        ? `<p style="margin: 8px 0; color: #374151;"><strong>Partner Name:</strong> ${project.partnerName}</p>`
        : ''

      return this.replaceTemplateVariables(template, {
        projectName: project.projectName,
        launchType: launchTypeDisplay,
        launchTypeLower,
        partnerNameSection,
        approvalDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        dashboardUrl: `https://launch.caishen.io/launch/${project.projectId}`,
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read project approval template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private replaceTemplateVariables(template: string, variables: Record<string, string>): string {
    let result = template

    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`
      result = result.replace(new RegExp(placeholder, 'g'), value)
    }

    return result
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    return emailRegex.test(email)
  }

  private getAdminPanelUrl(): string {
    const { adminPanelUrl } = getEnvConfig()

    // Use configured admin panel URL if available, otherwise use default
    return adminPanelUrl || ''
  }

  private getPayLaunchFeeUrl(projectId: string): string {
    return `https://launch.caishen.io/launch/${projectId}`
  }
}

import { Injectable, Inject, Logger, NotFoundException, ForbiddenException } from '@nestjs/common'
import { eq, and } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { projectAudits, projects } from '../../../libs/database/schema'
import { CreateAuditDto } from '../dto/create-audit.dto'
import { UpdateAuditDto } from '../dto/update-audit.dto'

@Injectable()
export class AuditsService {
  private readonly logger = new Logger(AuditsService.name)

  constructor(@Inject('DB') private db: NodePgDatabase<any>) {}

  async getProjectAudits(projectId: string) {
    // Verify project exists
    const project = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (project.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    const audits = await this.db
      .select()
      .from(projectAudits)
      .where(eq(projectAudits.projectId, projectId))
      .orderBy(projectAudits.createdAt)

    return audits
  }

  async create(projectId: string, createAuditDto: CreateAuditDto, userId: string) {
    // Verify project exists and user has access
    const project = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (project.length === 0) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    if (project[0].userId !== userId) {
      throw new ForbiddenException('You can only add audits to your own projects')
    }

    const newAudit = await this.db
      .insert(projectAudits)
      .values({
        projectId,
        auditDate: createAuditDto.auditDate,
        auditingFirm: createAuditDto.auditingFirm,
        auditReportUrl: createAuditDto.auditReportUrl,
        auditingFirmLogoUrl: createAuditDto.auditingFirmLogoUrl,
      })
      .returning()

    this.logger.log(`Created audit for project ${projectId} by user ${userId}`)

    return newAudit[0]
  }

  async update(auditId: string, updateAuditDto: UpdateAuditDto, userId: string, projectId: string) {
    // Verify audit exists and belongs to the project
    const audit = await this.db
      .select()
      .from(projectAudits)
      .where(and(eq(projectAudits.auditId, auditId), eq(projectAudits.projectId, projectId)))
      .limit(1)

    if (audit.length === 0) {
      throw new NotFoundException(`Audit with ID ${auditId} not found for this project`)
    }

    // Verify user has access to the project
    const project = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (project.length === 0 || project[0].userId !== userId) {
      throw new ForbiddenException('You can only update audits for your own projects')
    }

    const updatedAudit = await this.db
      .update(projectAudits)
      .set({
        auditDate: updateAuditDto.auditDate,
        auditingFirm: updateAuditDto.auditingFirm,
        auditReportUrl: updateAuditDto.auditReportUrl,
        auditingFirmLogoUrl: updateAuditDto.auditingFirmLogoUrl,
        updatedAt: new Date(),
      })
      .where(eq(projectAudits.auditId, auditId))
      .returning()

    this.logger.log(`Updated audit ${auditId} for project ${projectId} by user ${userId}`)

    return updatedAudit[0]
  }

  async delete(auditId: string, userId: string, projectId: string) {
    // Verify audit exists and belongs to the project
    const audit = await this.db
      .select()
      .from(projectAudits)
      .where(and(eq(projectAudits.auditId, auditId), eq(projectAudits.projectId, projectId)))
      .limit(1)

    if (audit.length === 0) {
      throw new NotFoundException(`Audit with ID ${auditId} not found for this project`)
    }

    // Verify user has access to the project
    const project = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    if (project.length === 0 || project[0].userId !== userId) {
      throw new ForbiddenException('You can only delete audits from your own projects')
    }

    await this.db.delete(projectAudits).where(eq(projectAudits.auditId, auditId))

    this.logger.log(`Deleted audit ${auditId} for project ${projectId} by user ${userId}`)

    return { success: true, message: 'Audit deleted successfully' }
  }

  async findById(auditId: string) {
    const audit = await this.db.select().from(projectAudits).where(eq(projectAudits.auditId, auditId)).limit(1)

    if (audit.length === 0) {
      throw new NotFoundException(`Audit with ID ${auditId} not found`)
    }

    return audit[0]
  }
}

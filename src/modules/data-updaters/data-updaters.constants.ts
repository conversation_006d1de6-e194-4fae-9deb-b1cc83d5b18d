export enum EventName {
  RatesUpdated = 'rates.updated',
}

export const BATCH_SIZE = 50

export const CATEGORY_MAPPING: Record<string, string> = {
  MARKET: 'Market',
  REGULATION: 'Regulation',
  TRADING: 'Market',
  XRP: 'Market',
  BTC: 'Bitcoin',
  BLOCKCHAIN: 'Technology',
  ETH: 'Ethereum',
  EXCHANGE: 'Market',
  MINING: 'Technology',
  TECHNOLOGY: 'Technology',
  DEFI: 'DeFi',
  RESEARCH: 'Research',
}

export const NEWS_CATEGORIES = [
  { name: 'All News', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Market', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Research', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Regulation', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Technology', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'DeFi', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Bitcoin', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Ethereum', color: '#000000', bgColor: '#E9E9E9', icon: null },
]

export const TOKENS_CATEGORIES = [
  { name: 'All Tokens', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Meme', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'DeFi', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'GameFi', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'AI', color: '#000000', bgColor: '#E9E9E9', icon: null },
  { name: 'Infra', color: '#000000', bgColor: '#E9E9E9', icon: null },
]

export const CHAIN_CATEGORIES = [
  {
    name: 'All Networks',
    color: '#000000',
    bgColor: '#E9E9E9',
    icon: null,
  },
  {
    name: 'Ethereum',
    color: '#000000',
    bgColor: '#E9E9E9',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png',
  },
  {
    name: 'Solana',
    color: '#000000',
    bgColor: '#E9E9E9',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png',
  },
  {
    name: 'Base',
    color: '#000000',
    bgColor: '#E9E9E9',
    icon: 'https://github.com/base-org/brand-kit/raw/main/logo/symbol/Base_Symbol_Blue.svg',
  },
  {
    name: 'BNB',
    color: '#000000',
    bgColor: '#E9E9E9',
    icon: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1839.png',
  },
]

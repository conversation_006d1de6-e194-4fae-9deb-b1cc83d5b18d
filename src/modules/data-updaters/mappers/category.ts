export const mapTokenCategory = (rawCategories: string[]): string[] => {
  if (!rawCategories || !Array.isArray(rawCategories)) {
    return ['All Tokens']
  }

  const categoryMapping: Record<string, string> = {
    'Decentralized Exchange': 'DeFi',
    DeFi: 'DeFi',
    'Yield Farming': 'DeFi',
    'Lending/Borrowing': 'DeFi',
    'Decentralized Finance (DeFi)': 'DeFi',
    'Decentralized Exchange (DEX)': 'DeFi',
    'Lending/Borrowing Protocols': 'DeFi',
    'Automated Market Maker (AMM)': 'DeFi',
    'BitStarters Launchpad': 'DeFi',
    'Camelot Launchpad': 'DeFi',
    'Decentralized Science (DeSci)': 'DeFi',
    'Algorithmic Stablecoin': 'DeFi',
    'Commodity-backed Stablecoin': 'DeFi',
    'real-world-assets': 'DeFi',
    'Binance Launchpad': 'DeFi',
    'Binance Launchpool': 'DeFi',

    dex: 'DeFi',
    defi: 'DeFi',
    web3: 'DeFi',
    Web3: 'DeFi',
    restaking: 'DeFi',
    dapp: 'DeFi',
    defai: 'DeFi',
    'cross-chain-dex-aggregator': 'DeFi',
    'liquid-staking-derivatives': 'DeFi',
    'medium-of-exchange': 'DeFi',
    substrate: 'DeFi',
    'dao-maker': 'DeFi',
    dao: 'DeFi',
    'decentralized-exchange-dex-token': 'DeFi',
    dag: 'DeFi',

    Gaming: 'GameFi',
    gaming: 'GameFi',
    gambling: 'GameFi',
    'Play to Earn': 'GameFi',
    'Gaming (GameFi)': 'GameFi',
    'gaming-platform': 'GameFi',
    'gaming-utility-token': 'GameFi',
    'gaming-governance-token': 'GameFi',
    'gaming-governance-protocol': 'GameFi',
    'Gaming Guild': 'GameFi',
    'Animal Racing': 'GameFi',
    Breeding: 'GameFi',
    'play-to-earn': 'GameFi',
    'pump-fun-ecosystem': 'GameFi',
    'Pump Science Ecosystem': 'GameFi',
    metaverse: 'GameFi',
    'collectibles-nfts': 'GameFi',
    sports: 'GameFi',
    'fan-token': 'GameFi',
    soccer: 'GameFi',
    philanthropy: 'GameFi',

    'AI & Big Data': 'AI',
    'Artificial Intelligence': 'AI',
    'AI Agents': 'AI',
    'AI Agent Launchpad': 'AI',
    DePIN: 'AI',
    depin: 'AI',
    'ai-big-data': 'AI',
    'Artificial Intelligence (AI)': 'AI',
    'ai-memes': 'AI',
    'generative-ai': 'AI',
    'ai-agents': 'AI',
    'virtuals-protocol-ecosystem': 'AI',
    'terminal-of-truths': 'AI',
    'data-provenance': 'AI',

    Meme: 'Meme',
    Memes: 'Meme',
    memes: 'Meme',
    'Meme Coin': 'Meme',
    'political-memes': 'Meme',
    'animal-memes': 'Meme',
    'cat-themed': 'Meme',
    'doggone-doggerel': 'Meme',
    'bonk-fun-ecosystem': 'Meme',
    'presale-memes': 'Meme',
    'ip-memes': 'Meme',
    'four-meme-ecosystem': 'Meme',

    payments: 'Infra',
    platform: 'Infra',
    Infrastructure: 'Infra',
    Oracle: 'Infra',
    'Cross Chain': 'Infra',
    'Layer 2': 'Infra',
    'Layer 1': 'Infra',
    'Layer 0 (L0)': 'Infra',
    'Smart Contract Platform': 'Infra',
    'Layer 1 (L1)': 'Infra',
    'Layer 2 (L2)': 'Infra',
    'layer-0': 'Infra',
    'layer-1': 'Infra',
    'layer-2': 'Infra',
    'smart-contracts': 'Infra',
    poi: 'Infra',
    'enterprise-solutions': 'Infra',
    masternodes: 'Infra',
    'move-vm': 'Infra',
    media: 'Infra',
    loyalty: 'Infra',

    'Bitcoin Sidechains': 'Infra',
    SideChain: 'Infra',
    'Modular Blockchain': 'Infra',
    'Market-Making Solution': 'Infra',
    'Telegram Apps': 'Infra',
    'Discord Bots': 'Infra',
    'AI Applications': 'AI',
    'AI Meme': 'Meme',
    'Alleged SEC Securities': 'Infra',
    Analytics: 'Infra',
    'Anime-Themed': 'Meme',
    Appchains: 'Infra',
    Art: 'Infra',
    'ASC-20': 'Infra',
    'Atomicals (ARC-20)': 'Infra',
    'Augmented Reality': 'Infra',
    'Avalanche L1': 'Infra',
    'Base Meme': 'Meme',
    'Big Data': 'Infra',
    'Binance HODLer Airdrops': 'Infra',
    'Binance Megadrop': 'Infra',
    'Bitcoin Fork': 'Infra',
    'BRC-20': 'Infra',
    'Bridge Governance Tokens': 'Infra',
    BTCfi: 'DeFi',
    'Business Platform': 'Infra',
    'Business Services': 'Infra',
    services: 'Infra',
    'distributed-computing': 'Infra',
    'Card Games': 'GameFi',
    'Cat-Themed': 'Meme',
    'Celebrity-Themed': 'Meme',
    'Celer Network': 'Infra',
    'Centralized Exchange (CEX) Product': 'Infra',
    'Centralized Finance (CeFi)': 'DeFi',
    'ChainGPT Launchpad': 'AI',
    'Christmas Themed': 'Meme',
    'CNY Stablecoin': 'DeFi',
    Collectibles: 'Infra',
    Communication: 'Infra',
    collectibles: 'Infra',
    communication: 'Infra',
    'Compound Tokens': 'DeFi',
    'Country-Themed Meme': 'Meme',
    'Cross-chain Communication': 'Infra',
    'Crypto-backed Stablecoin': 'DeFi',
    'Crypto-Backed Tokens': 'DeFi',
    Cryptocurrency: 'Infra',
    cryptocurrency: 'Infra',
    cToken: 'DeFi',
    Cybersecurity: 'Infra',
    cybersecurity: 'Infra',
    'DaoMaker Launchpad': 'DeFi',
    'Data Availability': 'Infra',
    'Decentralized Identifier (DID)': 'Infra',
    'DeFi Index': 'DeFi',
    DeLabs: 'Infra',
    Derivatives: 'DeFi',
    'Desci Meme': 'Meme',
    'Dex Aggregator': 'DeFi',
    'DN-404': 'Infra',
    'Dog-Themed': 'Meme',
    'Doodles LLC': 'Infra',
    'DRC-20': 'Infra',
    'Duck-Themed': 'Meme',
    'E-commerce': 'Infra',
    Education: 'Infra',
    education: 'Infra',
    'Elon Musk-Inspired': 'Meme',
    'Emoji-Themed': 'Meme',
    Energy: 'Infra',
    energy: 'Infra',
    Entertainment: 'Infra',
    entertainment: 'Infra',
    ERC20i: 'Infra',
    'ERC 404': 'Infra',
    ETF: 'DeFi',
    'Eth 2.0 Staking': 'DeFi',
    'Ethereum PoS IOU': 'Infra',
    'Ethereum PoW IOU': 'Infra',
    'EUR Stablecoin': 'DeFi',
    'Exchange-based Tokens': 'DeFi',
    'F1 Partnership': 'Infra',
    'Fan Token': 'GameFi',
    'Farming-as-a-Service (FaaS)': 'DeFi',
    'Farming Games': 'GameFi',
    'Fiat-backed Stablecoin': 'DeFi',
    'Fighting Games': 'GameFi',
    'Finance / Banking': 'DeFi',
    'Fixed Interest': 'DeFi',
    'Floor Protocol Tokens': 'DeFi',
    'Fractionalized NFT': 'Infra',
    'Frog-Themed': 'Meme',
    'FTX Holdings': 'Infra',
    'Gambling (GambleFi)': 'GameFi',
    'Gaming Blockchains': 'Infra',
    'Gaming Governance Token': 'GameFi',
    'Gaming Platform': 'GameFi',
    'Gaming Utility Token': 'GameFi',
    'GBP Stablecoin': 'DeFi',
    'Gig Economy': 'Infra',
    'GMCI 30 Index': 'Infra',
    'GMCI DeFi Index': 'DeFi',
    'GMCI DePIN Index': 'Infra',
    'GMCI Index': 'Infra',
    'GMCI Layer 1 Index': 'Infra',
    'GMCI Layer 2 Index': 'Infra',
    'GMCI Meme Index': 'Meme',
    Gotchiverse: 'GameFi',
    gotchiverse: 'GameFi',
    Governance: 'Infra',
    governance: 'Infra',
    'Guild and Scholarship': 'GameFi',
    Healthcare: 'Infra',
    healthcare: 'Infra',
    'Hybrid Token Standards': 'Infra',
    'HyperXpad Launchpad': 'DeFi',
    'IDR Stablecoin': 'DeFi',
    'Impossible Finance Launchpad': 'DeFi',
    Index: 'DeFi',
    index: 'DeFi',
    'Index Coop Defi Index': 'DeFi',
    'Index Coop Index': 'DeFi',
    'Index Coop Metaverse Index': 'GameFi',
    Inscriptions: 'Infra',
    Insurance: 'DeFi',
    Intent: 'Infra',
    inscriptions: 'Infra',
    insurance: 'DeFi',
    intent: 'Infra',
    'Internet of Things (IOT)': 'Infra',
    Interoperability: 'Infra',
    interoperability: 'Infra',
    synthetics: 'Infra',
    Investment: 'Infra',
    'IOU tokens': 'Infra',
    'JPY Stablecoin': 'DeFi',
    'Kommunitas Launchpad': 'DeFi',
    'KRW Stablecoin': 'DeFi',
    'Large-Cap PFP': 'Infra',
    Launchpad: 'DeFi',
    'Layer 3 (L3)': 'Infra',
    Legal: 'Infra',
    'Leveraged Token': 'DeFi',
    'Liquid Restaked ETH': 'DeFi',
    'Liquid Restaked SOL': 'DeFi',
    'Liquid Restaking Governance Tokens': 'DeFi',
    'Liquid Restaking Tokens': 'DeFi',
    'Liquid Staked APT': 'DeFi',
    'Liquid Staked ETH': 'DeFi',
    'Liquid Staked SOL': 'DeFi',
    'Liquid Staking': 'DeFi',
    'Liquid Staking Governance Tokens': 'DeFi',
    'Liquid Staking Tokens': 'DeFi',
    'LP Tokens': 'DeFi',
    LRTfi: 'Infra',
    LSDFi: 'DeFi',
    Manufacturing: 'Infra',
    Marketing: 'Infra',
    'Mascot-Themed': 'Meme',
    Masternodes: 'Infra',
    Media: 'Infra',
    'Memecoin NFTs': 'Meme',
    'Memorial Themed': 'Meme',
    Metagovernance: 'Infra',
    Metaverse: 'GameFi',
    'MEV Protection': 'Infra',
    'Mid-Cap PFP': 'Infra',
    'Milady And Derivatives': 'Meme',
    'Mirrored Assets': 'DeFi',
    'Move To Earn': 'GameFi',
    'Murad Picks': 'Infra',
    Music: 'Infra',
    'Name Service': 'Infra',
    NFT: 'Infra',
    'NFT Aggregator': 'DeFi',
    'NFT AMM': 'DeFi',
    'NFT Collections That Received Airdrops': 'Infra',
    'NFT Derivatives': 'DeFi',
    NFTFi: 'DeFi',
    'NFT Index': 'DeFi',
    'NFT Launchpad': 'DeFi',
    'NFT Lending/Borrowing': 'DeFi',
    'NFT Marketplace': 'DeFi',
    marketplace: 'DeFi',
    'Niftex Shards': 'Infra',
    NounsDAO: 'Infra',
    Number: 'Infra',
    'Ohm Fork': 'DeFi',
    'On-chain Gaming': 'GameFi',
    Options: 'DeFi',
    'PAAL AI Launchpad': 'AI',
    'Parallelized EVM': 'Infra',
    'Parody Meme': 'Meme',
    'Payment Solutions': 'Infra',
    Perpetuals: 'DeFi',
    'PFP / Avatar': 'Infra',
    'Play To Earn': 'GameFi',
    PolitiFi: 'Infra',
    'Poolz Finance Launchpad': 'DeFi',
    'Prediction Markets': 'DeFi',
    'Presale Meme': 'Meme',
    'Privacy Blockchain': 'Infra',
    'Privacy Coins': 'Infra',
    privacy: 'Infra',
    'zero-knowledge-proofs': 'Infra',
    'Proof of Stake (PoS)': 'Infra',
    'Proof of Work (PoW)': 'Infra',
    Protocol: 'Infra',
    'Quest-to-Earn': 'GameFi',
    'Racing Games': 'GameFi',
    'RealT Tokens': 'Infra',
    'Real World Assets (RWA)': 'DeFi',
    'Rebase Tokens': 'DeFi',
    Recruitment: 'Infra',
    'Reddit Points': 'Infra',
    'Regenerative Finance (ReFi)': 'DeFi',
    Remittance: 'Infra',
    Restaking: 'Infra',
    Retail: 'Infra',
    Rollup: 'Infra',
    'Rollups-as-a-Service (RaaS)': 'Infra',
    RPG: 'GameFi',
    Runes: 'Infra',
    'RWA Protocol': 'DeFi',
    Seigniorage: 'DeFi',
    'SGD Stablcoin': 'DeFi',
    'Shooting Games': 'GameFi',
    'Simulation Games': 'GameFi',
    SocialFi: 'AI',
    'Software as a service': 'Infra',
    'Solana Meme': 'Meme',
    'Solana Token-2022': 'Infra',
    SPL22: 'Infra',
    Sports: 'GameFi',
    'Sports Games': 'GameFi',
    'SRC-20': 'Infra',
    'Stablecoin Protocol': 'DeFi',
    Stablecoins: 'DeFi',
    'Sticker-Themed Coins': 'Meme',
    Storage: 'Infra',
    'Strategy Games': 'GameFi',
    'Structured Products': 'DeFi',
    'Sui Meme': 'Meme',
    Synthetic: 'DeFi',
    'Synthetic Asset': 'DeFi',
    'Synthetic Issuer': 'DeFi',
    'Tap to Earn': 'GameFi',
    'Technology & Science': 'Infra',
    tempCategory: 'Infra',
    'Terminal of Truths': 'Infra',
    'Terraport Launchpad': 'DeFi',
    'The Boy’s Club': 'Infra',
    'TokenFi Launchpad': 'DeFi',
    'Tokenized Assets': 'DeFi',
    'Tokenized BTC': 'DeFi',
    'Tokenized Commodities': 'DeFi',
    'Tokenized Gold': 'DeFi',
    'Tokenized Real Estate': 'DeFi',
    'real-estate': 'DeFi',
    'Tokenized Silver': 'DeFi',
    'Tokenized Stock': 'DeFi',
    'Tokenized Treasury Bills (T-Bills)': 'DeFi',
    'Tokenized Treasury Bonds (T-Bonds)': 'DeFi',
    'Token Standards': 'Infra',
    'TON Meme': 'Meme',
    Tourism: 'Infra',
    'TRON Meme': 'Meme',
    'TRY Stablecoin': 'DeFi',
    'USD Stablecoin': 'DeFi',
    'US Election 2020': 'Infra',
    'Virtual Reality': 'Infra',
    VPN: 'Infra',
    Wallets: 'Infra',
    wallet: 'Infra',
    'Wall Street Bets Themed': 'Meme',
    'Web 2 Brands': 'Infra',
    'Wojak-Themed': 'Meme',
    'Wormhole Assets': 'Infra',
    'Wrapped-Tokens': 'Infra',
    'Yearn Vault Tokens': 'DeFi',
    'Yield Aggregator': 'DeFi',
    'Yield Optimizer': 'DeFi',
    'Yield Tokenization Product': 'DeFi',
    'Yield Tokenization Protocol': 'DeFi',
    'Zero Knowledge (ZK)': 'Infra',
    'Zodiac-Themed': 'Meme',
    'Zoo-Themed': 'Meme',
    'communications-social-media': 'Infra',
    'content-creation': 'Infra',
    'internet-capital-markets': 'Infra',
    'social-token': 'Infra',
  }

  return [
    'All Tokens',
    ...new Set(
      rawCategories.map((category) => categoryMapping[category]).filter(Boolean), // Remove undefined entries
    ),
  ]
}

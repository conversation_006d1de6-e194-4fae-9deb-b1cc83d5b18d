export const mapTokenByChain = (categories?: string[]): string[] => {
  if (!categories || !Array.isArray(categories)) {
    return ['All Networks']
  }

  const networkMapping: Record<string, string> = {
    'Ethereum Ecosystem': 'Ethereum',
    'Base Ecosystem': 'Base',
    'Solana Ecosystem': 'Solana',
    'BNB Chain Ecosystem': 'BNB',
    'BNB Chain': 'BNB',
    'Polygon Ecosystem': 'Polygon',
    'Avalanche Ecosystem': 'Avalanche',
    'Arbitrum Ecosystem': 'Arbitrum',
    ethereum: 'Ethereum',
    base: 'Base',
    solana: 'Solana',
    bnb: 'BNB',
    'binance-smart-chain': 'BNB',
    'arbitrum-one': 'Arbitrum',
    'polygon-pos': 'Polygon',
    polygon: 'Polygon',
    avalanche: 'Avalanche',
    arbitrum: 'Arbitrum',
    Ethereum: 'Ethereum',
    Base: 'Base',
    Solana: 'Solana',
    BNB: 'BNB',
    Polygon: 'Polygon',
    Avalanche: 'Avalanche',
    Arbitrum: 'Arbitrum',
  }

  return ['All Networks', ...new Set(categories.map((category) => networkMapping[category]).filter(Boolean))]
}

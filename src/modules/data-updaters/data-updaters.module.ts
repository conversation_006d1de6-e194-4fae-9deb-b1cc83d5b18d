import { Module } from '@nestjs/common'

import { IndexerModule } from '@/libs/indexer'
import { ChainsModule } from '@/modules/chains'
import { DiscoverModule } from '@/modules/discover'
import { EarnModule } from '@/modules/earn'
import { RampModule } from '@/modules/ramp'
import { TokensModule } from '@/modules/tokens'

import { CashRampClient } from './clients/cashramp'
import { CmcClient } from './clients/cmc'
import { CoinDeskClient } from './clients/coin-desk'
import { CoingeckoClient } from './clients/coingecko'
import { LunarCrushClient } from './clients/lunar-crush'
import { MoonPayClient } from './clients/moon-pay'
import { OnrampMoneyClient } from './clients/onramp-money'
import { PixPayClient } from './clients/pix-pay'
import { YellowCardClient } from './clients/yellow-card'
import { MarketDataUpdater, RatesUpdater, DiscoverUpdater, TokensUpdater, RampUpdater } from './updaters'

@Module({
  imports: [TokensModule, DiscoverModule, EarnModule, RampModule, ChainsModule, IndexerModule],
  providers: [
    MarketDataUpdater,
    RatesUpdater,
    DiscoverUpdater,
    TokensUpdater,
    RampUpdater,

    CoingeckoClient,
    CmcClient,
    LunarCrushClient,
    CoinDeskClient,
    CashRampClient,
    MoonPayClient,
    OnrampMoneyClient,
    PixPayClient,
    YellowCardClient,
  ],
})
export class DataUpdatersModule {}

import { Injectable, Logger } from '@nestjs/common'

import { RateLimitedClient } from '@/abstract/rate-limited-client'
import { getEnvConfig } from '@/config/env'
import type { ChainInfo } from '@/modules/chains'
import { type Country, type Offer, USER_ADDRESS_PARAM } from '@/modules/ramp'
import type { TokenInfo } from '@/modules/tokens'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  MOONPAY_SUPPORTED_TOKENS,
  MOONPAY_WIDGET_URL_BUY,
  MOONPAY_WIDGET_URL_SELL,
  MOONPAY_PROVIDER_LOGO_URL,
} from './moon-pay.constants'
import type { MoonPayCountryInfo, MoonPayCurrencyInfo } from './moon-pay.types'

@Injectable()
export class MoonPayClient extends RateLimitedClient {
  private logger = new Logger('MoonPayClient')

  constructor() {
    super({
      clientConfig: {
        baseURL: 'https://api.moonpay.com/v3',
        timeout: 60000,
      },
      durationBetweenRequests: 1000,
    })
  }

  async getRampData(tokens: TokenInfo[], chains: ChainInfo[], countries: Country[]): Promise<Offer[]> {
    try {
      const { moonPay } = getEnvConfig()
      const [moonPayCountries, moonPayCurrencies] = await Promise.all([
        this.client.get<MoonPayCountryInfo[]>('/countries'),
        this.client.get<MoonPayCurrencyInfo[]>('/currencies'),
      ])

      const supportedTokens = moonPayCurrencies.data.filter((currency) => {
        if (currency.type !== 'crypto' || currency.isSuspended) {
          return false
        }

        const lowerCode = currency.code.toLowerCase()

        return Object.keys(MOONPAY_SUPPORTED_TOKENS).some((tokenKey) =>
          MOONPAY_SUPPORTED_TOKENS[tokenKey].chains.some((chain) => chain.moonpayTicker === lowerCode),
        )
      })

      const offers: Offer[] = []
      const existedOffers = new Set<string>()

      for (const currency of supportedTokens) {
        const baseCode = currency.code.split('_')[0].toUpperCase()
        const token = tokens.find((t) => t.ticker === baseCode)

        if (!token) {
          this.logger.warn(`Token not found in our database: ${baseCode}`)
          continue
        }

        const chainInfo = this.findChainForCurrency(currency, chains)

        if (!chainInfo) {
          this.logger.warn(`Chain not found for currency ${currency.code}`)
          continue
        }

        const supportedCountries = moonPayCountries.data.filter(
          (country) =>
            country.isBuyAllowed && country.isAllowed && !(currency.notAllowedCountries || []).includes(country.alpha2),
        )

        for (const country of supportedCountries) {
          const countryInfo = countries.find((c) => c.alpha3Code === country.alpha3)

          if (!countryInfo) {
            this.logger.warn(`Country not found in our database: ${country.alpha3}`)
            continue
          }

          for (const fiatCurrency of countryInfo.supportedTokens) {
            const tokenId = token.id
            const providerName = moonPay.name
            const id = `${providerName}-${tokenId}-${fiatCurrency}`

            if (existedOffers.has(id)) continue

            offers.push({
              id,
              fiatCurrency,
              tokenId,
              providerName,
              chainId: chainInfo.id,
              paymentMethods: ['Credit Card', 'Debit Card'],
              providerLogoUrl: MOONPAY_PROVIDER_LOGO_URL,
              providerFees: null,
              onrampUrl: this.buildMoonPayUrl({
                currencyCode: currency.code,
                countryCode: fiatCurrency,
                type: 'buy',
              }),
              offrampUrl: null,
            })
            existedOffers.add(id)
          }
        }
      }

      return offers
    } catch (error) {
      this.logger.error(`Error fetching MoonPay data: ${getErrorMessage(error)}`)

      return []
    }
  }

  private findChainForCurrency(currency: MoonPayCurrencyInfo, chains: ChainInfo[]) {
    const baseCode = currency.code.split('_')[0].toUpperCase()

    if (!MOONPAY_SUPPORTED_TOKENS[baseCode]) return null

    let chainInfo = null

    const matchedChain = MOONPAY_SUPPORTED_TOKENS[baseCode].chains.find(
      (sc) => sc.moonpayTicker === currency.code.toLowerCase(),
    )

    if (matchedChain) {
      chainInfo = chains.find((c) => c.id === matchedChain.chainId)
    }

    if (!chainInfo) {
      const defaultChainId = MOONPAY_SUPPORTED_TOKENS[baseCode].defaultChain
      chainInfo = chains.find((c) => c.id === defaultChainId)
    }

    if (!chainInfo) {
      const chainId = currency.metadata?.chainId
      const networkCode = currency.metadata?.networkCode

      chainInfo = chains.find((c) => c.chainId?.toString() === chainId || c.id === networkCode)
    }

    return chainInfo
  }

  private buildMoonPayUrl({
    currencyCode,
    countryCode,
    type,
  }: {
    currencyCode: string
    countryCode: string
    type: 'buy' | 'sell'
  }) {
    const { moonPay } = getEnvConfig()

    const urlParams = new URLSearchParams({
      apiKey: moonPay.publicKey,
      currencyCode: currencyCode.toLowerCase(),
      walletAddress: USER_ADDRESS_PARAM,
      baseCurrencyCode: countryCode.toLowerCase(),
      showWalletAddressForm: 'false',
    })

    // If it's a sell transaction, use the sell endpoint
    const baseUrl = type === 'sell' ? MOONPAY_WIDGET_URL_SELL : MOONPAY_WIDGET_URL_BUY

    return `${baseUrl}?${urlParams.toString()}`
  }
}

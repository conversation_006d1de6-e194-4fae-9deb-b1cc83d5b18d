export interface MoonPayCurrencyInfo {
  id: string
  createdAt: string
  updatedAt: string
  type: 'crypto' | 'fiat'
  name: string
  code: string
  precision: number
  decimals: number | null
  icon: string
  maxAmount: number | null
  minAmount: number | null
  minBuyAmount: number | null
  maxBuyAmount: number | null
  isSellSupported: boolean
  isUtxoCompatible: boolean
  notAllowedUSStates?: string[]
  notAllowedCountries?: string[]
  addressRegex?: string
  testnetAddressRegex?: string
  supportsAddressTag?: boolean
  addressTagRegex?: null | string
  supportsTestMode?: boolean
  supportsLiveMode?: boolean
  isSuspended?: boolean
  isStableCoin?: boolean
  confirmationsRequired?: number | null
  minSellAmount?: number | null
  maxSellAmount?: number | null
  isSwapBaseSupported?: boolean
  isSwapQuoteSupported?: boolean
  isBaseAsset?: boolean
  isSupportedInUS?: boolean
  metadata?: {
    contractAddress: null | string
    coinType: null | string
    chainId: null | string
    networkCode: string
  }
}

type Document = 'driving_licence' | 'national_identity_card' | 'passport' | 'residence_permit'

export interface MoonPayCountryInfo {
  name: string
  alpha2: string
  alpha3: string
  continent: 'Africa' | 'Asia' | 'Europe' | 'North America' | 'Oceania' | 'South America'
  isAllowed: boolean
  isBuyAllowed: boolean
  isMoonPayBalanceAllowed: boolean
  isNftAllowed: boolean
  isSelfServeBlocked?: boolean
  isSellAllowed: boolean
  isBalanceLedgerWithdrawAllowed: boolean
  supportedDocuments: Document[]
  suggestedDocument?: Document
  isSelfServeHighRisk?: boolean
  states?: State[]
}

interface State {
  code: string
  name: string
  isAllowed: boolean
  isBuyAllowed: boolean
  isMoonPayBalanceAllowed: boolean
  isNftAllowed: boolean
  isSellAllowed: boolean
  isBalanceLedgerWithdrawAllowed: boolean
  nftAllowedPartners?: string[]
  nftRestrictedFlows?: string[]
}

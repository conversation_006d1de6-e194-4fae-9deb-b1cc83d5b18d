export const MOONPAY_WIDGET_URL_BUY = 'https://buy.moonpay.com'
export const MOONPAY_WIDGET_URL_SELL = 'https://sell.moonpay.com'
export const MOONPAY_PROVIDER_LOGO_URL = 'https://avatars.githubusercontent.com/u/45370368?s=200&v=4'

interface MoonPayTokenInfo {
  chains: { chainId: string; moonpayTicker: string }[]
  defaultChain: string
}

export const MOONPAY_SUPPORTED_TOKENS: Record<string, MoonPayTokenInfo> = {
  USDT: {
    chains: [
      { chainId: 'optimism', moonpayTicker: 'usdt_optimism' },
      // {'chainId': 'arbitrum', 'moonpayTicker': 'usdt_arbitrum'},
      // {'chainId': 'base', 'moonpayTicker': 'usdt_base'},
      // {'chainId': 'polygon', 'moonpayTicker': 'usdt_polygon'},
      // {'chainId': 'solana', 'moonpayTicker': 'usdt_solana'},
      // {'chainId': 'ethereum', 'moonpayTicker': 'usdt'},
    ],
    defaultChain: 'optimism',
  },
  USDC: {
    chains: [
      { chainId: 'optimism', moonpayTicker: 'usdc_optimism' },
      // {'chainId': 'arbitrum', 'moonpayTicker': 'usdc_arbitrum'},
      // {'chainId': 'base', 'moonpayTicker': 'usdc_base'},
      // {'chainId': 'polygon', 'moonpayTicker': 'usdc_polygon'},
      // {'chainId': 'solana', 'moonpayTicker': 'usdc_solana'},
      // {'chainId': 'ethereum', 'moonpayTicker': 'usdc'},
    ],
    defaultChain: 'optimism',
  },
}

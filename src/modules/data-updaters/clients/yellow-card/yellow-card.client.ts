import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '@/config/env'
import type { ChainInfo } from '@/modules/chains'
import { type Offer, USER_ADDRESS_PARAM, YELLOW_CARD_SIGNATURE_PARAM } from '@/modules/ramp'
import type { TokenInfo } from '@/modules/tokens'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  YELLOW_CARD_NETWORK_TO_CHAIN_NAME,
  YELLOW_CARD_PROVIDER_LOGO_URL,
  YELLOW_CARD_SERVICE_FEES,
  YELLOW_CARD_SUPPORTED_COUNTRIES,
  YELLOW_CARD_SUPPORTED_TOKENS,
} from './yellow-card.constants'

@Injectable()
export class YellowCardClient {
  private logger = new Logger('YellowCardClient')

  async getRampData(tokens: TokenInfo[], chains: ChainInfo[]): Promise<Offer[]> {
    try {
      const result: Offer[] = []
      const seen = new Set<string>()
      const { name } = getEnvConfig().yellowCard

      for (const [countryCode, countryInfo] of Object.entries(YELLOW_CARD_SUPPORTED_COUNTRIES)) {
        const paymentMethods = []

        if (countryInfo.bankTransfer) paymentMethods.push('Bank Transfer')

        if (countryInfo.mobileMoney) paymentMethods.push('Mobile Money')

        if (paymentMethods.length === 0) continue

        for (const [tokenSymbol, tokenConfig] of Object.entries(YELLOW_CARD_SUPPORTED_TOKENS)) {
          const tokenInfo = tokens.find((curr) => curr.ticker.toLowerCase() === tokenSymbol.toLowerCase())

          if (!tokenInfo) continue

          const tokenId = tokenInfo.id
          const fiatCurrency = countryInfo.currency
          const providerName = 'Onramp.Money'
          const id = `${providerName}-${tokenId}-${fiatCurrency}`

          if (seen.has(id)) continue

          seen.add(id)

          const network = tokenConfig.defaultNetwork
          const chain = chains.find((c) => c.id === YELLOW_CARD_NETWORK_TO_CHAIN_NAME[network])

          if (!chain) continue

          const tokenChain = tokenInfo.chains.find((tc) => tc.id === chain.id)

          if (!tokenChain) continue

          result.push({
            id,
            fiatCurrency,
            tokenId,
            chainId: chain.id,
            paymentMethods,
            providerName: name,
            providerLogoUrl: YELLOW_CARD_PROVIDER_LOGO_URL,
            providerFees:
              paymentMethods.length === 1
                ? YELLOW_CARD_SERVICE_FEES[paymentMethods[0]]
                : `${Math.min(...paymentMethods.map((m) => parseFloat(YELLOW_CARD_SERVICE_FEES[m])))}%-${Math.max(...paymentMethods.map((m) => parseFloat(YELLOW_CARD_SERVICE_FEES[m])))}%`,
            onrampUrl: this.buildUrl(countryCode, tokenSymbol, 'buy', network),
            offrampUrl: this.buildUrl(countryCode, tokenSymbol, 'sell', network),
          })
        }
      }

      return result
    } catch (error) {
      this.logger.error(`Error generating YellowCard data: ${getErrorMessage(error)}`)

      return []
    }
  }

  private buildUrl(countryCode: string, token: string, type: 'buy' | 'sell', network: string) {
    const { apiKey } = getEnvConfig().yellowCard
    const baseUrl = new URL(`https://widget.yellowcard.io/landing/${apiKey}`)
    const params = {
      token,
      network: network || YELLOW_CARD_SUPPORTED_TOKENS[token].defaultNetwork,
      currencyAmount: '100',
      walletAddress: USER_ADDRESS_PARAM,
      localCurrency: YELLOW_CARD_SUPPORTED_COUNTRIES[countryCode].currency,
      txType: type.toLowerCase(),
      signature: YELLOW_CARD_SIGNATURE_PARAM,
    }

    Object.entries(params).forEach(([key, value]) => {
      baseUrl.searchParams.append(key, value)
    })

    return baseUrl.toString()
  }
}

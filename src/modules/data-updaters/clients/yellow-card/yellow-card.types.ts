export interface OnrampMoneyPaymentMethodsResponse {
  status: number
  code: number
  data: { [key: string]: { [key: string]: number } }
}

export interface OnrampMoneyConfigResponse {
  status: number
  code: number
  data: OnrampMoneyConfig
}

interface OnrampMoneyConfig {
  onRampFee: number
  gatewayFee: number
  clientFee: number
  isClientFeeFlat: number
  isOnRampFeeFlat: number
  isGatewayFeeFlat: number
  name: string
  supportedCoins: string
  supportedFiat: number[]
  onrampGatewayFee: { [key: string]: { [key: string]: OnrampGatewayFee } }
  specificCountryCoinLive: Record<string, number[]>
  specificAppIdCoinLive: Record<string, number[]>
  country: string
}

interface OnrampGatewayFee {
  fee: number
  isGatewayFeeFlat: number
  cryptoFee?: {
    coinId: number
    chainId: number
    fee: number
    isFeeFlat: number
  }
  maxFee?: number
  onrampFee?: number
  isOnrampFeeFlat?: number
}

export interface OnrampMoneyCountryConfigResponse {
  status: number
  code: number
  data: OnrampMoneyCountryConfigs
}

interface OnrampMoneyCountryConfigs {
  buy: { [key: string]: OnrampMoneyCountryConfig }
  sell: { [key: string]: OnrampMoneyCountryConfig }
}

interface OnrampMoneyCountryConfig {
  isActive: number
  paymentMethods: { [key: string]: number }
}

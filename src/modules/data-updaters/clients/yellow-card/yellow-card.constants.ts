export const YELLOW_CARD_PROVIDER_LOGO_URL =
  'https://yellowcard-website.cdn.prismic.io/yellowcard-website/15e5ea2f-f96b-45aa-b614-ed7eb791b572_Yellow+Card+Logo.svg'

export const YELLOW_CARD_SUPPORTED_COUNTRIES: Record<
  string,
  { name: string; currency: string; bankTransfer: boolean; mobileMoney: boolean }
> = {
  NG: { name: 'Nigeria', currency: 'NGN', bankTransfer: true, mobileMoney: false },
  KE: { name: 'Kenya', currency: 'KES', bankTransfer: true, mobileMoney: true },
  ZA: { name: 'South Africa', currency: 'ZAR', bankTransfer: true, mobileMoney: false },
  CM: { name: 'Cameroon', currency: 'XAF', bankTransfer: false, mobileMoney: true },
  ZM: { name: 'Zambia', currency: 'ZMW', bankTransfer: true, mobileMoney: true },
  BW: { name: 'Botswana', currency: 'BWP', bankTransfer: true, mobileMoney: false },
  RW: { name: 'Rwanda', currency: 'RWF', bankTransfer: true, mobileMoney: false },
  MW: { name: 'Malawi', currency: 'MWK', bankTransfer: true, mobileMoney: false },
  UG: { name: 'Uganda', currency: 'UGX', bankTransfer: true, mobileMoney: false },
  BJ: { name: 'Benin', currency: 'XOF', bankTransfer: false, mobileMoney: true },
  CI: { name: 'Ivory Coast', currency: 'XOF', bankTransfer: false, mobileMoney: true },
  TG: { name: 'Togo', currency: 'XOF', bankTransfer: false, mobileMoney: true },
}

export const YELLOW_CARD_NETWORK_TO_CHAIN_NAME: Record<string, string> = {
  ERC20: 'ethereum',
  SOL: 'solana',
}

export const YELLOW_CARD_SUPPORTED_TOKENS: Record<string, { networks: string[]; defaultNetwork: string }> = {
  USDT: {
    networks: ['SOL'],
    defaultNetwork: 'SOL',
  },
  USDC: {
    networks: ['SOL'],
    defaultNetwork: 'SOL',
  },
}

export const YELLOW_CARD_SERVICE_FEES: Record<string, string> = {
  'Bank Transfer': '1%',
  'Mobile Money': '2%',
}

import { Injectable } from '@nestjs/common'
import { z } from 'zod'

import { RateLimitedClient } from '@/abstract/rate-limited-client'
import { getEnvConfig } from '@/config/env'
import { type News, newsSchema } from '@/modules/discover'

import { type CoinDeskLatestNewsResponse, type CoinDeskNewsCategory } from './coin-desk.types'
import { CATEGORY_MAPPING } from '../../data-updaters.constants'

@Injectable()
export class CoinDeskClient extends RateLimitedClient {
  constructor() {
    const { coinDesk } = getEnvConfig()

    super({
      clientConfig: {
        baseURL: 'https://data-api.coindesk.com',
        timeout: 60000,
        params: { api_key: coinDesk.apiKey },
      },
      retryConfig: {
        retryDelay: (retryCount) => 10000 * retryCount,
      },
      durationBetweenRequests: 10000,
    })
  }

  async getLatestNews(): Promise<News[]> {
    await this.checkRateLimit()

    const { coinDesk } = getEnvConfig()
    const { data } = await this.client<CoinDeskLatestNewsResponse>(`/news/v1/article/list`, {
      params: { lang: 'EN', limit: 50, source_ids: coinDesk.newsSources.join(',') },
    })

    const news: News[] = data.Data.map((article) => ({
      id: String(article.ID),
      headline: article.TITLE,
      source: article.SOURCE_DATA.NAME,
      sourceImageUrl: article.SOURCE_DATA.IMAGE_URL,
      sourceUrl: article.URL,
      time: article.PUBLISHED_ON * 1000, // convert to milliseconds
      categories: this.mapCategories(article.CATEGORY_DATA),
    }))

    return z.array(newsSchema).parse(news)
  }

  private mapCategories(sourceCategories: CoinDeskNewsCategory[]) {
    if (!Array.isArray(sourceCategories)) return ['All News']

    return ['All News', ...new Set(sourceCategories.map((cat) => CATEGORY_MAPPING[cat.NAME]).filter(Boolean))]
  }
}

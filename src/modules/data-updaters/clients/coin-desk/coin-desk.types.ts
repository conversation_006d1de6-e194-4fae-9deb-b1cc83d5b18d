export interface CoinDeskLatestNewsResponse {
  Data: CoinDeskNewsData[]
  Err: unknown
}

interface CoinDeskNewsData {
  TYPE: string
  ID: number
  GUID: string
  PUBLISHED_ON: number
  IMAGE_URL: string
  TITLE: string
  SUBTITLE: null | string
  AUTHORS: string
  URL: string
  SOURCE_ID: number
  BODY: string
  KEYWORDS: string
  LANG: 'EN'
  UPVOTES: number
  DOWNVOTES: number
  SCORE: number
  SENTIMENT: 'NEGATIVE' | 'NEUTRAL' | 'POSITIVE'
  STATUS: 'ACTIVE'
  CREATED_ON: number
  UPDATED_ON: number | null
  SOURCE_DATA: SourceData
  CATEGORY_DATA: CoinDeskNewsCategory[]
}

export interface CoinDeskNewsCategory {
  TYPE: string
  ID: number
  NAME: string
  CATEGORY: string
}

interface SourceData {
  TYPE: string
  ID: number
  SOURCE_KEY: string
  NAME: string
  IMAGE_URL: string
  URL: string
  LANG: 'EN'
  SOURCE_TYPE: 'RSS'
  LAUNCH_DATE: number
  SORT_ORDER: number
  BENCHMARK_SCORE: number
  STATUS: 'ACTIVE'
  LAST_UPDATED_TS: number
  CREATED_ON: number
  UPDATED_ON: number
}

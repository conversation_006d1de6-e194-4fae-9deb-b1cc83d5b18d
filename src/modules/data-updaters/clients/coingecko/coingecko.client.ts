import { Injectable } from '@nestjs/common'

import { RateLimitedClient } from '@/abstract/rate-limited-client'
import type { MarketData } from '@/modules/tokens'

import type { CoingeckoCoinResponse } from './coingecko.types'

@Injectable()
export class CoingeckoClient extends RateLimitedClient {
  constructor() {
    super({
      clientConfig: { baseURL: 'https://api.coingecko.com', timeout: 60000 },
      retryConfig: {
        retries: 5,
        retryDelay: (retryCount) => 10000 * retryCount,
        retryCondition: (error) => error.status === 429,
      },
      durationBetweenRequests: 20000,
    })
  }

  async getMarketData(coingeckoId: string | number): Promise<Partial<MarketData> | undefined> {
    await this.checkRateLimit()

    const { data } = await this.client.get<CoingeckoCoinResponse>(`/api/v3/coins/${coingeckoId}`, {
      params: {
        localization: false,
        tickers: false,
        market_data: true,
        community_data: false,
        developer_data: false,
        sparkline: false,
      },
    })

    return {
      tokenRank: data.market_cap_rank,
      fdv: data.market_data.fully_diluted_valuation?.usd,
      totalSupply: data.market_data.total_supply,
      circulatingSupply: data.market_data.circulating_supply,
      tradingVolume24h: data.market_data.total_volume.usd,
      liquidityDepth: this.determineLiquidityDepth(data.market_cap_rank),
    }
  }

  private determineLiquidityDepth(rank: number): MarketData['liquidityDepth'] {
    if (rank <= 20) return 'excellent'

    if (rank <= 100) return 'good'

    if (rank <= 1000) return 'moderate'

    return 'poor'
  }
}

import { z } from 'zod'

import { marketDataFromStorageSchema, rateFromStorageSchema } from '@/modules/tokens/storage/tokens-storage.schema'

import { CmcTokenInfo } from './types/token-info'

export const tokenInfoMapper = (data: CmcTokenInfo) => {
  const cmcId = String(data.id)

  return {
    cmcId,
    ticker: data.symbol,
    fullName: data.name,
    imageUrl: z.string(),

    chains: z.array(
      z.object({
        id: z.string(),
        address: z.string(),
        decimals: z.number(),
        isNative: z.boolean().optional(),
      }),
    ),

    isStable: z.boolean(),
    isViewOnly: z.boolean(),
    isTradable: z.boolean().optional(),

    marketData: marketDataFromStorageSchema,

    categories: z.array(z.string()),
    isDefault: z.boolean().optional(),
    description: z.string().optional(),

    rateData: rateFromStorageSchema.optional(),

    launchDate: z.string().optional().nullable(),

    links: z
      .object({
        website: z.string().optional(),
        twitter: z.string().optional(),
        telergam: z.string().optional(),
        discord: z.string().optional(),
        warpcast: z.string().optional(),
        instagram: z.string().optional(),
      })
      .optional(),

    displayOptions: z
      .object({
        displayTicker: z.string().optional(),
        isChartHidden: z.boolean().optional(),
        subtitle: z.string().optional(),
        isAvailableForOnramp: z.boolean().optional(),
      })
      .optional(),

    updatedAt: z.string(),
  }
}

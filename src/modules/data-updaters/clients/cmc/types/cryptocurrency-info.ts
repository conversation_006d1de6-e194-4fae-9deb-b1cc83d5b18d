import type { CmcApiStatus } from './api-status'

export interface CmcCryptoCurrencyInfo {
  status: CmcApiStatus
  data: Record<string, CurrencyInfo>
}

interface CurrencyInfo {
  id: number
  name: string
  symbol: string
  category: string
  description: string
  slug: string
  logo: string
  subreddit: string
  notice: string
  tags: string[]
  'tag-names': string[]
  'tag-groups': string[]
  urls: Urls
  platform: CoinClass
  date_added: string
  twitter_username: string
  is_hidden: number
  date_launched: null
  contract_address: ContractAddress[]
  self_reported_circulating_supply: null
  self_reported_tags: null
  self_reported_market_cap: null
  infinite_supply: boolean
}

interface ContractAddress {
  contract_address: string
  platform: ContractAddressPlatform
}

interface ContractAddressPlatform {
  name: string
  coin: CoinClass
}

interface CoinClass {
  id: string
  name: string
  symbol: string
  slug: string
  token_address?: string
}

interface Urls {
  website: string[]
  twitter: string[]
  message_board: any[]
  chat: string[]
  facebook: any[]
  explorer: string[]
  reddit: any[]
  technical_doc: string[]
  source_code: any[]
  announcement: string[]
}

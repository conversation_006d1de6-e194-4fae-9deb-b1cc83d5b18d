import type { CmcApiStatus } from './api-status'

export interface CmcHistoricalQuote {
  status: CmcApiStatus
  data: { [key: string]: TokenData }
}

interface TokenData {
  quotes: QuoteElement[]
  id: number
  name: string
  symbol: string
  is_active: number
  is_fiat: number
}

interface QuoteElement {
  timestamp: Date
  quote: QuoteQuote
}

interface QuoteQuote {
  USD: QuoteInfo
}

interface QuoteInfo {
  percent_change_1h: number
  percent_change_24h: number
  percent_change_7d: number
  percent_change_30d: number
  price: number
  volume_24h: number
  market_cap: number
  total_supply: number
  circulating_supply: number
  timestamp: Date
}

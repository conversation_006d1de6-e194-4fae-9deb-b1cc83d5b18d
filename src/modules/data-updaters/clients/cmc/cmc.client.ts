import { Injectable, Logger } from '@nestjs/common'
import dayjs from 'dayjs'

import { RateLimitedClient } from '@/abstract/rate-limited-client'
import { getEnvConfig } from '@/config/env'
import type { HistoryPoint, MarketData, Rate, TokenInfo } from '@/modules/tokens'

import type { CmcCryptoCurrencyInfo } from './types/cryptocurrency-info'
import type { CmcGainersLosersResponse } from './types/gainers-losers'
import type { CmcHistoricalQuote } from './types/historical-quotes'
import type { CmcLatestQuotes } from './types/latest-quotes'
import type { CmcNewListings } from './types/new-listings'

@Injectable()
export class CmcClient extends RateLimitedClient {
  private logger = new Logger('CmcClient')

  constructor() {
    const { coinMarketCap } = getEnvConfig()

    super({
      clientConfig: {
        baseURL: coinMarketCap.apiUrl,
        headers: { 'X-CMC_PRO_API_KEY': coinMarketCap.apiKey },
        timeout: 60000,
      },
      durationBetweenRequests: 1000,
    })
  }

  async getMarketData(cmcIds: string[]): Promise<Record<string, Partial<MarketData>>> {
    await this.checkRateLimit()

    const response = await this.client.get<CmcLatestQuotes>(`/v2/cryptocurrency/quotes/latest`, {
      params: { id: cmcIds.join(',') },
    })

    const result: Record<string, Partial<MarketData>> = {}

    for (const [cmcId, tokenData] of Object.entries(response.data.data)) {
      const quoteData = tokenData.quote.USD
      const tokenRank = tokenData.cmc_rank || 100000000

      result[cmcId] = {
        tokenRank,
        fdv: quoteData.fully_diluted_market_cap || undefined,
        totalSupply: tokenData.total_supply || undefined,
        circulatingSupply: tokenData.circulating_supply || undefined,
        tradingVolume24h: quoteData.volume_24h || undefined,
        liquidityDepth: this.determineLiquidityDepth(tokenRank),
      }
    }

    return result
  }

  async getHistoricalRates(cmcIds: string[]): Promise<Record<string, Omit<Rate, 'tokenId'>>> {
    await this.checkRateLimit()

    const { data: historical } = await this.client.get<CmcHistoricalQuote>('/v3/cryptocurrency/quotes/historical', {
      params: {
        id: cmcIds.join(','),
        interval: '1h',
        count: 24,
        convert: 'USD',
      },
    })

    const result: Record<string, Omit<Rate, 'tokenId'>> = {}

    for (const [cmcId, data] of Object.entries(historical.data)) {
      const rateHistory = data.quotes.map((quote) => ({
        value: quote.quote.USD.price,
        timestamp: new Date(quote.timestamp).getTime(),
      }))
      const rate = this.calculateRateData(rateHistory)

      if (!rate) {
        this.logger.warn(
          `Cannot calculate rate data for token with cmcId ${cmcId}. History points received: ${rateHistory.length}`,
        )

        continue
      }

      result[cmcId] = rate
    }

    return result
  }

  async getNewTokens(): Promise<string[]> {
    await this.checkRateLimit()

    const { data } = await this.client.get<CmcNewListings>('/v1/cryptocurrency/listings/new', {
      params: { limit: 110 },
    })

    return data.data
      .filter((t) => dayjs(t.date_added).diff() < 1000 * 60 * 60 * 24)
      .map((t) => String(t.id))
      .slice(0, 100)
  }

  async getTopGainersLosers(isLosers?: boolean): Promise<string[]> {
    await this.checkRateLimit()

    const { data } = await this.client.get<CmcGainersLosersResponse>('/v1/cryptocurrency/trending/gainers-losers', {
      params: { limit: 500, sort_dir: isLosers ? 'asc' : 'desc' },
    })

    return data.data
      .filter((t) => t.cmc_rank && t.cmc_rank <= 1500)
      .map((t) => String(t.id))
      .slice(0, 100)
  }

  async getTopByMarketCap(): Promise<string[]> {
    await this.checkRateLimit()

    const { data } = await this.client.get<CmcGainersLosersResponse>('/v1/cryptocurrency/listings/latest', {
      params: { limit: 100, sort: 'market_cap' },
    })

    return data.data.map((t) => String(t.id))
  }

  async getTokenInfo(cmcIds: string[]): Promise<{ tokenInfo: Omit<TokenInfo, 'id'>; rate: Omit<Rate, 'tokenId'> }[]> {
    await this.checkRateLimit()
    const { data: tokensDataObj } = await this.client.get<CmcCryptoCurrencyInfo>(`/v2/cryptocurrency/info`, {
      params: { id: cmcIds.join(',') },
    })
    const marketDataObj = await this.getMarketData(cmcIds)
    const historicalRates = await this.getHistoricalRates(cmcIds)

    const tokens: { tokenInfo: Omit<TokenInfo, 'id'>; rate: Omit<Rate, 'tokenId'> }[] = []

    for (const cmcId of cmcIds) {
      const tokenData = tokensDataObj.data[cmcId]
      const marketData = marketDataObj[cmcId]
      const rate = historicalRates[cmcId]

      if (!tokenData || !marketData || !rate) {
        this.logger.warn(`Cannot get token info for token with cmcId ${cmcId}`)

        continue
      }

      const categories = []

      if (tokenData.platform) categories.push(tokenData.platform.name)

      for (const platform of tokenData.contract_address) {
        if (platform.platform) categories.push(platform.platform.name)
      }

      const tokenRank = marketData.tokenRank || 100000000
      const isStable = tokenData.tags?.includes('stablecoin') || false
      const ticker = tokenData.symbol

      tokens.push({
        rate,
        tokenInfo: {
          cmcId,
          ticker,
          fullName: tokenData.name,
          imageUrl: tokenData.logo || '',
          marketData: {
            ...marketData,
            launchDate: tokenData.date_launched || tokenData.date_added,
            categories: [...(tokenData.tags || []), ...(tokenData.self_reported_tags || []), ...categories],
            description: tokenData.description || '',
            tokenRank,
            fdv: marketData.fdv || 0,
            totalSupply: marketData.totalSupply || 0,
            circulatingSupply: marketData.circulatingSupply || 0,
            tradingVolume24h: marketData.tradingVolume24h || 0,
            liquidityDepth: this.determineLiquidityDepth(tokenRank),
            links: {
              website: tokenData.urls.website?.[0] || '',
              twitter: tokenData.urls.twitter?.[0] || '',
            },
          },
          displayInfo: {
            ticker,
            isChartHidden: !isStable,
            subtitle: isStable ? 'Stablecoin' : undefined,
          },
          chains: [],
          isStable,
          isViewOnly: true,
          isDefault: false,
          isTradable: false,
          isLaunchpadToken: false,
        },
      })
    }

    return tokens
  }

  private determineLiquidityDepth(rank: number): MarketData['liquidityDepth'] {
    if (rank <= 20) return 'excellent'

    if (rank <= 100) return 'good'

    if (rank <= 1000) return 'moderate'

    return 'poor'
  }

  private calculateRateData(rateHistory: HistoryPoint[]): Omit<Rate, 'tokenId'> | undefined {
    if (!rateHistory?.length) return undefined

    const percentChange = this.calculatePercentChange(rateHistory)

    return {
      rate: String(rateHistory[rateHistory.length - 1].value),
      percentChange: percentChange.toFixed(2),
      rateHistory: rateHistory.slice(-24),
    }
  }

  private calculatePercentChange(rateHistory: HistoryPoint[]): number {
    if (rateHistory.length < 2) return 0

    const oldestPrice = rateHistory[0].value
    const currentPrice = rateHistory[rateHistory.length - 1].value

    return Number((((currentPrice - oldestPrice) / oldestPrice) * 100).toFixed(2))
  }
}

import { Injectable, Logger } from '@nestjs/common'

import type { ChainInfo } from '@/modules/chains'
import { type Offer, USER_ADDRESS_PARAM } from '@/modules/ramp'
import type { TokenInfo } from '@/modules/tokens'
import { getErrorMessage } from '@/utils/get-error-message'

import { PIX_PAY_BASE_URL, PIX_PAY_LOGO_URL } from './pix-pay.constants'

@Injectable()
export class PixPayClient {
  private logger = new Logger('PixPayClient')

  async getRampData(tokens: TokenInfo[], chains: ChainInfo[]): Promise<Offer[]> {
    try {
      const usdt = tokens.find((currency) => currency.ticker.toLowerCase() === 'usdt')

      if (!usdt) {
        this.logger.warn('USDT not found in tokens')

        return []
      }

      const polygonChain = chains.find(
        (chain) => chain.ticker.toLowerCase() === 'matic' || chain.ticker.toLowerCase() === 'polygon',
      )

      if (!polygonChain) {
        this.logger.error('Polygon chain not found')

        return []
      }

      const usdtPolygonChain = usdt.chains.find((chain) => chain.id === polygonChain.id)

      if (!usdtPolygonChain) {
        this.logger.error('Polygon chain not found for USDT')

        return []
      }

      const fiatCurrency = 'BRL'
      const tokenId = usdt.id
      const providerName = 'PixPay'
      const id = `${providerName}-${tokenId}-${fiatCurrency}`

      return [
        {
          id,
          fiatCurrency,
          tokenId,
          providerName,
          chainId: polygonChain.id,
          paymentMethods: ['PiX'],
          providerLogoUrl: PIX_PAY_LOGO_URL,
          providerFees: '3% + 1 BRL',
          onrampUrl: this.buildOnRampUrl(usdtPolygonChain.address),
          offrampUrl: null,
        },
      ]
    } catch (error) {
      this.logger.error(`Error generating PixPay data: ${getErrorMessage(error)}`)

      return []
    }
  }

  private buildOnRampUrl(tokenAddress: string, amount = '1.00') {
    const urlParams = new URLSearchParams({
      action: 'buy',
      currency: 'USDT',
      amount,
      address: USER_ADDRESS_PARAM,
    })

    return `${PIX_PAY_BASE_URL}?${urlParams.toString()}`
  }
}

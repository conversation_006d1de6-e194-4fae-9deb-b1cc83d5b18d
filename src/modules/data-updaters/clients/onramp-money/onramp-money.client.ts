import { createHmac } from 'crypto'

import { Injectable, Logger } from '@nestjs/common'

import { RateLimitedClient } from '@/abstract/rate-limited-client'
import { getEnvConfig } from '@/config/env'
import { type Country, type Offer, USER_ADDRESS_PARAM } from '@/modules/ramp'
import type { TokenInfo } from '@/modules/tokens'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  ONRAMP_MONEY_FIAT_TYPE_TO_CURRENCY,
  ONRAMP_MONEY_NETWORK_TO_CHAIN_ID,
  ONRAMP_MONEY_PAYMENT_METHOD_MAPPING,
  ONRAMP_MONEY_SUPPORTED_COINS,
} from './onramp-money.constants'
import type {
  OnrampMoneyConfigResponse,
  OnrampMoneyCountryConfigResponse,
  OnrampMoneyPaymentMethodsResponse,
} from './onramp-money.types'

@Injectable()
export class OnrampMoneyClient extends RateLimitedClient {
  private logger = new Logger('OnrampMoneyClient')

  constructor() {
    super({
      clientConfig: {
        baseURL: 'https://api.onramp.money',
        timeout: 60000,
      },
      durationBetweenRequests: 1000,
    })
  }

  async getRampData(tokens: TokenInfo[], countries: Country[]): Promise<Offer[]> {
    try {
      const { appId } = getEnvConfig().onrampMoney
      const countryConfig = await this.fetchCountryConfig()
      const { data: paymentMethods } = await this.client<OnrampMoneyPaymentMethodsResponse>(
        'https://api.onramp.money/onramp/api/v2/common/public/fetchPaymentMethodType',
      )
      const { data: ourConfig } = await this.client<OnrampMoneyConfigResponse>(
        `https://api.onramp.money/onramp/api/v2/buy/public/merchantDetails?appId=${appId}`,
      )
      const { supportedFiat } = ourConfig.data

      const result: Offer[] = []
      const existedOffers = new Set<string>()

      // Process each supported coin
      for (const [coinCode, networks] of Object.entries(ONRAMP_MONEY_SUPPORTED_COINS)) {
        const tokenInfo = tokens.find((token) => token.ticker.toLowerCase() === coinCode.toLowerCase())

        if (!tokenInfo) continue

        for (const network of networks) {
          const chainId = ONRAMP_MONEY_NETWORK_TO_CHAIN_ID[network]

          if (!chainId) continue

          for (const [fiatType, countryData] of Object.entries(countryConfig.data.buy)) {
            if (!countryData.isActive || !ONRAMP_MONEY_FIAT_TYPE_TO_CURRENCY[fiatType]) continue

            const fiatCurrency = ONRAMP_MONEY_FIAT_TYPE_TO_CURRENCY[fiatType]
            const tokenId = tokenInfo.id
            const providerName = 'Onramp.Money'
            const id = `${providerName}-${tokenId}-${fiatCurrency}`

            if (existedOffers.has(id)) continue

            if (!supportedFiat.includes(parseInt(fiatType, 10))) continue

            const countryInfo = countries.find((c) => c.supportedTokens.includes(fiatCurrency))

            if (!countryInfo) continue

            const countryPaymentMethods = paymentMethods.data[fiatType] || {}
            const availablePaymentMethods = Object.keys(countryPaymentMethods).map((method) =>
              method.replace(/_/g, ' ').toLowerCase(),
            )

            const onrampUrl = new URL('https://onramp.money/app')
            onrampUrl.searchParams.append('appId', appId)
            onrampUrl.searchParams.append('coinCode', coinCode)
            onrampUrl.searchParams.append('network', network)
            onrampUrl.searchParams.append('fiatType', fiatType)
            onrampUrl.searchParams.append('walletAddress', USER_ADDRESS_PARAM)

            result.push({
              id,
              fiatCurrency,
              tokenId,
              chainId,
              providerName,
              paymentMethods: availablePaymentMethods.map(
                (method) => ONRAMP_MONEY_PAYMENT_METHOD_MAPPING[method.toUpperCase()] || method,
              ),
              providerLogoUrl: 'https://onramp.money/assets/favicon.png',
              providerFees: null,
              onrampUrl: onrampUrl.toString(),
              offrampUrl: null,
            })
            existedOffers.add(id)
          }
        }
      }

      return result
    } catch (error) {
      this.logger.error(`Error fetching Onramp.Money data: ${getErrorMessage(error)}`)

      return []
    }
  }

  private async fetchCountryConfig(): Promise<OnrampMoneyCountryConfigResponse> {
    try {
      await this.checkRateLimit()

      const { secretKey, apiKey } = getEnvConfig().onrampMoney
      const payloadBase64 = Buffer.from(JSON.stringify({})).toString('base64')
      const signature = createHmac('sha512', secretKey).update(payloadBase64).digest('base64')

      const { data } = await this.client.get<OnrampMoneyCountryConfigResponse>(
        '/onramp/api/v2/common/public/fetchAllCountryConfig',
        {
          headers: {
            'X-ONRAMP-SIGNATURE': signature,
            'X-ONRAMP-APIKEY': apiKey,
            'X-ONRAMP-PAYLOAD': payloadBase64,
          },
        },
      )

      return data
    } catch (error) {
      this.logger.error(`Error fetching country config: ${getErrorMessage(error)}`)

      throw error
    }
  }
}

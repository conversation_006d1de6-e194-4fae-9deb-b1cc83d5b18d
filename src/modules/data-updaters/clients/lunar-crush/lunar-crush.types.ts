export interface LunarCrushResponse {
  config: LunarCrushConfig
  data: LunarCrushTweetInfo[]
}

interface LunarCrushTweetInfo {
  id: string
  post_type: 'tweet'
  post_title: string
  post_created: number
  post_sentiment: number
  post_link: string
  post_time_interactions: number
  interactions_total: number
  creator_id: string
  creator_name: string
  creator_display_name: string
  creator_followers: number
  creator_avatar: string
}

interface LunarCrushConfig {
  start: number
  network: string
  id: string
  type: string
  end: number
  generated: number
}

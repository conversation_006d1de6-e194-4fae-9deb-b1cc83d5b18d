import { Injectable } from '@nestjs/common'
import { z } from 'zod'

import { RateLimitedClient } from '@/abstract/rate-limited-client'
import { getEnvConfig } from '@/config/env'
import { type Highlight, highlightSchema } from '@/modules/discover'

import { type LunarCrushResponse } from './lunar-crush.types'

@Injectable()
export class LunarCrushClient extends RateLimitedClient {
  constructor() {
    const { lunarCrush } = getEnvConfig()

    super({
      clientConfig: {
        baseURL: 'https://lunarcrush.com',
        timeout: 60000,
        headers: {
          Authorization: `Bearer ${lunarCrush.apiKey}`,
          Accept: '*/*',
          'X-Lunar-Client': 'yolo',
        },
      },
      retryConfig: {
        retryDelay: (retryCount) => 10000 * retryCount,
      },
      durationBetweenRequests: 10000,
    })
  }

  async getTwitterPostsForLatestWeek(twitterId: string): Promise<Highlight[]> {
    await this.checkRateLimit()

    const { data } = await this.client<LunarCrushResponse>(`/api3/public/creator/twitter/${twitterId}/posts/v1`)
    const highlights = data.data.map((tweet) => ({
      id: tweet.id,
      type: 'twitter',
      headline: tweet.post_title,
      source: `@${tweet.creator_name}`,
      time: tweet.post_created * 1000,
      url: tweet.post_link || `https://x.com/${tweet.creator_name}/status/${tweet.id}`,
    }))

    return z.array(highlightSchema).parse(highlights)
  }
}

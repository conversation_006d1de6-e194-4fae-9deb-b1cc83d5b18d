import { Injectable, Logger } from '@nestjs/common'
import CashRamp from 'cashramp'

import { getEnvConfig } from '@/config/env'
import { type Country, type Offer, USER_ADDRESS_PARAM } from '@/modules/ramp'
import type { TokenInfo } from '@/modules/tokens'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  CASHRAMP_BASE_URL,
  CASHRAMP_NETWORK_FEES,
  CASHRAMP_NETWORK_TO_CHAIN_ID,
  CASHRAMP_PREFERRED_NETWORKS,
} from './cashramp.constants'

@Injectable()
export class CashRampClient {
  private logger = new Logger('CashRampClient')
  private client: CashRamp
  private publicKey: string

  constructor() {
    const { secretKey, publicKey } = getEnvConfig().cashRamp

    this.client = new CashRamp({ env: 'live', secretKey })
    this.publicKey = publicKey
  }

  async getRampData(tokens: TokenInfo[], countries: Country[]): Promise<Offer[]> {
    const [assetsResponse, countriesResponse, limitsResponse] = await Promise.all([
      this.client.getRampableAssets(),
      this.client.getAvailableCountries(),
      this.client.getRampLimits(),
    ])

    if (
      !assetsResponse.success ||
      !assetsResponse.result ||
      !countriesResponse.success ||
      !countriesResponse.result ||
      !limitsResponse.success ||
      !limitsResponse.result
    ) {
      throw new Error('Failed to fetch data from CashRamp API')
    }

    const accrueStables = assetsResponse.result?.filter(
      (asset) => (asset.symbol === 'USDC' || asset.symbol === 'USDT') && asset.networks?.length > 0,
    )

    if (!accrueStables || !accrueStables.length) return []

    const result: Offer[] = []
    const existedOffers = new Set<string>()

    for (const accrueStable of accrueStables) {
      const tokenInfo = tokens.find((curr) => curr.ticker.toLowerCase() === accrueStable.symbol.toLowerCase())

      if (!tokenInfo) {
        this.logger.warn(`No matching token found for symbol ${accrueStable.symbol}`)
        continue
      }

      const preferredNetwork = CASHRAMP_PREFERRED_NETWORKS.find(
        (net) => accrueStable.networks.includes(net) && accrueStable.contractAddress?.[net],
      )
      const firstAvailableNetwork = Object.keys(accrueStable.contractAddress || {}).find(
        (net) => CASHRAMP_NETWORK_TO_CHAIN_ID[net],
      )
      const network = preferredNetwork || firstAvailableNetwork

      if (!network) continue

      // Process each country for the current stablecoin
      for (const country of countriesResponse.result) {
        try {
          const countryInfo = countries.find((c) => c.alpha2Code === country.code)

          if (!countryInfo) {
            this.logger.warn(`No matching country found for code ${country.code}`)
            continue
          }

          const fiatCurrency = countryInfo.supportedTokens[0]
          const tokenId = tokenInfo.id
          const providerName = 'Accrue'
          const id = `${providerName}-${tokenId}-${fiatCurrency}`

          if (existedOffers.has(id)) {
            continue
          }

          const paymentMethodsResponse = await this.client.getPaymentMethodTypes({ country: country.id.toString() })

          if (!paymentMethodsResponse.success || !paymentMethodsResponse.result) {
            this.logger.warn(`Failed to get payment methods for ${country.code}:`, paymentMethodsResponse.error)
            continue
          }

          const uniquePaymentMethods = [
            // @ts-ignore wrong typing in the library
            ...new Set(paymentMethodsResponse.result.map((method) => method.label as string).filter(Boolean)),
          ]

          const baseRampUrl = new URL('/hosted/ramp', CASHRAMP_BASE_URL)
          baseRampUrl.searchParams.append('key', this.publicKey)
          baseRampUrl.searchParams.append('coin', accrueStable.symbol)
          baseRampUrl.searchParams.append('network', network)
          baseRampUrl.searchParams.append('currency', countryInfo.supportedTokens[0])
          baseRampUrl.searchParams.append('address', USER_ADDRESS_PARAM)
          baseRampUrl.searchParams.append('isWalletContext', 'false')

          const onrampUrl = new URL(baseRampUrl)
          onrampUrl.searchParams.append('paymentType', 'deposit')

          const offrampUrl = new URL(baseRampUrl)
          offrampUrl.searchParams.append('paymentType', 'withdrawal')

          result.push({
            id,
            fiatCurrency,
            tokenId,
            providerName,
            chainId: CASHRAMP_NETWORK_TO_CHAIN_ID[network],
            paymentMethods: uniquePaymentMethods,
            providerLogoUrl: `https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/2f/3a/6a/2f3a6acf-1ca7-3c5e-9917-9e1f059aa927/AppIcon-0-0-1x_U007emarketing-0-7-0-0-85-220.png/246x0w.png`,
            providerFees: CASHRAMP_NETWORK_FEES[network] || null,
            onrampUrl: onrampUrl.toString(),
            offrampUrl: offrampUrl.toString(),
          })
          existedOffers.add(id)
        } catch (error) {
          this.logger.warn(`Error processing country ${country.code}: ${getErrorMessage(error)}`)
        }
      }
    }

    return result
  }
}

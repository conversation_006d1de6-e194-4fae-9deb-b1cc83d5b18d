import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { Cron, CronExpression } from '@nestjs/schedule'

import { getEnvConfig } from '@/config/env'
import { Big } from '@/libs/big-number'
import {
  DiscoverService,
  type Highlight,
  newsListSectionSchema,
  socialListSectionSchema,
  type TokensListSection,
} from '@/modules/discover'
import { type TokenInfo, TokensService } from '@/modules/tokens'
import { keyBy } from '@/utils/arrays/key-by'
import { getErrorMessage } from '@/utils/get-error-message'

import { TokensUpdater } from './tokens.updater'
import { CmcClient } from '../clients/cmc'
import { CoinDeskClient } from '../clients/coin-desk'
import { LunarCrushClient } from '../clients/lunar-crush'
import { CHAIN_CATEGORIES, EventName, NEWS_CATEGORIES, TOKENS_CATEGORIES } from '../data-updaters.constants'
import { mapTokenCategory } from '../mappers/category'
import { mapTokenByChain } from '../mappers/chain'

@Injectable()
export class DiscoverUpdater {
  private logger = new Logger('DiscoverUpdater')

  constructor(
    private tokensService: TokensService,
    private discoverService: DiscoverService,

    private tokenUpdater: TokensUpdater,

    private lunarCrushClient: LunarCrushClient,
    private coinDeskClient: CoinDeskClient,
    private cmcClient: CmcClient,
  ) {}

  @Cron(CronExpression.EVERY_2_HOURS, {
    waitForCompletion: true,
    name: 'cronUpdateSections',
    disabled: process.env.NODE_ENV !== 'production',
  })
  private async cronUpdateSections() {
    await this.updateSections()
  }

  @OnEvent(EventName.RatesUpdated)
  async handleRatesUpdated() {
    this.logger.log(`Received ${EventName.RatesUpdated}. Updating gainers/losers sections`)
    const sections = await this.discoverService.getDiscoverSections()
    const gainers = sections.find((section) => section.id === 'topgainers') as TokensListSection
    const losers = sections.find((section) => section.id === 'toplosers') as TokensListSection

    if (!gainers || !losers) {
      this.logger.error(`Cannot get ${!gainers ? 'gainers' : 'losers'}`)

      return
    }

    const sortedGainers = await this.sortTokensSection(gainers)
    const sortedLosers = await this.sortTokensSection(losers, true)

    await this.discoverService.setDiscoverSection(sortedGainers.id, sortedGainers, true)
    await this.discoverService.setDiscoverSection(sortedLosers.id, sortedLosers)

    this.logger.log('Updated gainers/losers sections')
  }

  async updateSections() {
    await this.updateLatestNews()
    await this.updateLatestTwitterPosts()
    await this.updateNewTokensSection()
    await this.updateTopMarketCapSection()

    const gainers = await this.getGainersSection()
    const losers = await this.getLosersSection()

    if (!gainers || !losers) {
      this.logger.error(`Cannot get ${!gainers ? 'gainers' : 'losers'}`)

      return
    }

    const sortedGainers = await this.sortTokensSection(gainers)
    const sortedLosers = await this.sortTokensSection(losers, true)

    await this.discoverService.setDiscoverSection(sortedGainers.id, sortedGainers, true)
    await this.discoverService.setDiscoverSection(sortedLosers.id, sortedLosers)

    await this.tokenUpdater
      .disableUnusedViewOnlyTokens()
      .catch((e) => this.logger.error(`Failed to disable unused view only tokens. Error: ${getErrorMessage(e)}`))
  }

  private async sortTokensSection(section: TokensListSection, isAsc?: boolean): Promise<TokensListSection> {
    const copySection = JSON.parse(JSON.stringify(section)) as TokensListSection
    const rates = await this.tokensService.getRates({})
    const ratesObj = keyBy(rates, (rate) => rate.tokenId)

    copySection.tokens.sort((a, b) => {
      const rateA = ratesObj[isAsc ? b.id : a.id].percentChange || -100
      const rateB = ratesObj[isAsc ? a.id : b.id].percentChange || -100

      return Big(rateB).minus(rateA).toNumber()
    })

    return copySection
  }

  private async updateLatestNews(): Promise<void> {
    try {
      const news = await this.coinDeskClient.getLatestNews()

      const activeCategories = news.reduce((acc, item) => {
        item.categories.forEach((category) => acc.add(category))

        return acc
      }, new Set<string>())

      const activeDisplayCategories = NEWS_CATEGORIES.filter(
        (category) => category.name === 'All News' || activeCategories.has(category.name),
      )

      const section = newsListSectionSchema.parse({
        news,
        id: 'marketNews',
        type: 'newsList',
        title: 'Market News',
        visibleCount: 4,
        filters: {
          categories: activeDisplayCategories,
        },
      })

      await this.discoverService.setDiscoverSection(section.id, section)
      this.logger.log('News section update completed successfully')
    } catch (error) {
      this.logger.error(error)
    }
  }

  private async updateLatestTwitterPosts(): Promise<void> {
    try {
      const { lunarCrush } = getEnvConfig()
      const allHighlights: Highlight[] = []

      for (const accountId of lunarCrush.twitterIds) {
        const highlights = await this.lunarCrushClient.getTwitterPostsForLatestWeek(accountId)
        allHighlights.push(...highlights)
      }

      allHighlights.sort((h1, h2) => h2.time - h1.time)

      const latestHighlights: Highlight[] = []
      const counts: Record<string, number> = {}

      allHighlights.forEach((h) => {
        const count = counts[h.source] || 1

        if (!count || count <= 3) {
          latestHighlights.push(h)
          counts[h.source] = count + 1
        }
      })

      const section = socialListSectionSchema.parse({
        id: 'header',
        type: 'twitter',
        title: 'Discover',
        highlights: allHighlights,
      })

      await this.discoverService.setDiscoverSection(section.id, section)
      this.logger.log('Twitter section update completed successfully')
    } catch (error) {
      this.logger.error(error)
    }
  }

  private async updateNewTokensSection(skipRevalidation?: boolean): Promise<void> {
    try {
      const newTokensCmcIds = await this.cmcClient.getNewTokens()
      const tokens = await this.addTokensForSection(newTokensCmcIds)

      const section: TokensListSection = {
        id: 'newtokens',
        type: 'tokensListHorizontal',
        title: 'New Tokens',
        visibleCount: 8,
        filters: { categories: CHAIN_CATEGORIES },
        tokens: tokens.map((t) => ({ id: t.id, tags: mapTokenByChain(t.marketData.categories) })),
        subtitle: null,
        icon: null,
      }

      await this.discoverService.setDiscoverSection(section.id, section, skipRevalidation)
      this.logger.log(`New tokens section update completed successfully`)
    } catch (e) {
      this.logger.error(e)
    }
  }

  private async updateTopMarketCapSection(skipRevalidation?: boolean): Promise<void> {
    try {
      const newTokensCmcIds = await this.cmcClient.getTopByMarketCap()
      const tokens = await this.addTokensForSection(newTokensCmcIds)

      const section: TokensListSection = {
        id: 'tokensbymarketcap',
        type: 'tokensListVertical',
        title: 'Tokens by Market Cap',
        visibleCount: 8,
        filters: { categories: this.formatTokensCategories(tokens) },
        tokens: tokens.map((t) => ({ id: t.id, tags: mapTokenCategory(t.marketData.categories) })),
        subtitle: '24h',
        icon: null,
      }

      await this.discoverService.setDiscoverSection(section.id, section, skipRevalidation)
      this.logger.log(`Top market cap section updated successfully`)
    } catch (e) {
      this.logger.error(e)
    }
  }

  private async getGainersSection(): Promise<TokensListSection | undefined> {
    try {
      const newTokensCmcIds = await this.cmcClient.getTopGainersLosers()
      const tokens = await this.addTokensForSection(newTokensCmcIds)

      return {
        id: 'topgainers',
        type: 'tokensListVertical',
        title: 'Top Gainers',
        visibleCount: 8,
        filters: { categories: this.formatTokensCategories(tokens) },
        tokens: tokens.map((t) => ({ id: t.id, tags: mapTokenCategory(t.marketData.categories) })),
        subtitle: '24h',
        icon: 'https://675f2ffc16ff1000312600ad--inclusivetokenstest.netlify.app/image/gainers.png',
      }
    } catch (e) {
      this.logger.error(e)

      return undefined
    }
  }

  private async getLosersSection(): Promise<TokensListSection | undefined> {
    try {
      const newTokensCmcIds = await this.cmcClient.getTopGainersLosers(true)
      const tokens = await this.addTokensForSection(newTokensCmcIds)

      return {
        id: 'toplosers',
        type: 'tokensListVertical',
        title: 'Top Losers',
        visibleCount: 8,
        filters: { categories: this.formatTokensCategories(tokens) },
        tokens: tokens.map((t) => ({ id: t.id, tags: mapTokenCategory(t.marketData.categories) })),
        subtitle: '24h',
        icon: 'https://675f2ffc16ff1000312600ad--inclusivetokenstest.netlify.app/image/loosers.png',
      }
    } catch (e) {
      this.logger.error(e)

      return undefined
    }
  }

  private async addTokensForSection(tokensCmcIds: string[]): Promise<TokenInfo[]> {
    const tokens = await this.tokensService.getTokens({})
    const tokensByCmcId = keyBy(tokens, (token) => token.cmcId)

    const tokensToCreate: string[] = []
    const existedTokens: TokenInfo[] = []

    tokensCmcIds.forEach((cmcId) => {
      const tokenInfo = tokensByCmcId[cmcId]

      if (!tokenInfo) {
        tokensToCreate.push(cmcId)

        return
      }

      existedTokens.push(tokenInfo)
    })

    let savedTokens: TokenInfo[] = []

    if (tokensToCreate.length > 0) {
      const generatedTokens = await this.cmcClient.getTokenInfo(tokensToCreate)
      savedTokens = await this.tokensService.addTokens(generatedTokens)
    }

    const tokensObj = keyBy(savedTokens.concat(existedTokens), (t) => t.cmcId)

    return tokensCmcIds.reduce((acc, cmcId) => {
      const tokenInfo = tokensObj[cmcId]

      if (!tokenInfo) {
        this.logger.error(`No tokenInfo found for cmcId ${cmcId}`)

        return acc
      }

      return acc.concat(tokenInfo)
    }, [] as TokenInfo[])
  }

  private formatTokensCategories(tokens: TokenInfo[]) {
    const activeCategories = tokens.reduce((acc, item) => {
      const categories = mapTokenCategory(item.marketData.categories)

      categories.forEach((category) => {
        const currentCount = acc.get(category) || 0
        const nextCount = currentCount + 1

        acc.set(category, nextCount)
      })

      return acc
    }, new Map<string, number>())

    return TOKENS_CATEGORIES.filter(
      (category) => category.name === 'All Tokens' || (activeCategories.get(category.name) || 0) > 10,
    )
  }
}

import { Injectable, Logger, Inject } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { <PERSON>ron } from '@nestjs/schedule'
import { eq } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import * as schema from '@/libs/database/schema'
import { projects } from '@/libs/database/schema'
import { PriceIndexerService } from '@/libs/indexer'
import { type Rate, type TokenInfo, TokensService } from '@/modules/tokens'
import { keyBy } from '@/utils/arrays/key-by'
import { splitIntoChunks } from '@/utils/arrays/split-into-chunks'

import { CmcClient } from '../clients/cmc'
import { BATCH_SIZE, EventName } from '../data-updaters.constants'

@Injectable()
export class RatesUpdater {
  private logger = new Logger('RatesUpdater')

  constructor(
    private tokensService: TokensService,
    private cmcClient: CmcClient,
    private priceIndexerService: PriceIndexerService,
    private eventEmitter: EventEmitter2,
    @Inject('DB') private db: NodePgDatabase<typeof schema>,
  ) {}

  // every 20 min
  @Cron('0 */20 * * * *', {
    waitForCompletion: true,
    name: 'cronUpdateRates',
    disabled: process.env.NODE_ENV !== 'production',
  })
  private async cronUpdateRates() {
    await this.updateAllRates(20 * 60 * 1000)
  }

  async updateAllRates(interval?: number): Promise<void> {
    const tokens = await this.tokensService.getTokens({})

    // Split tokens into regular and launchpad tokens
    const regularTokens = tokens.filter((token) => !token.isLaunchpadToken)
    const launchpadTokens = tokens.filter((token) => token.isLaunchpadToken)

    this.logger.log(
      `Found ${regularTokens.length} regular tokens and ${launchpadTokens.length} launchpad tokens to update`,
    )

    // Update both types in parallel
    const [regularRates, launchpadRates] = await Promise.allSettled([
      this.fetchRegularTokenRates(regularTokens, interval),
      this.fetchLaunchpadTokenRates(launchpadTokens, interval),
    ])

    // Combine results
    const allRates: Rate[] = []

    if (regularRates.status === 'fulfilled') {
      allRates.push(...regularRates.value)
    } else {
      this.logger.error('Failed to update regular token prices:', regularRates.reason)
    }

    if (launchpadRates.status === 'fulfilled') {
      allRates.push(...launchpadRates.value)
    } else {
      this.logger.error('Failed to update launchpad token prices:', launchpadRates.reason)
    }

    // Update rates in storage
    if (allRates.length > 0) {
      await this.tokensService.updateRates(allRates)
      this.logger.log(`Successfully updated ${allRates.length} token prices`)
    } else {
      this.logger.warn('No token prices were updated')
    }

    this.eventEmitter.emit(EventName.RatesUpdated)
  }

  /**
   * Update regular tokens using CMC (existing logic)
   */
  private async fetchRegularTokenRates(tokens: TokenInfo[], updateInterval?: number): Promise<Rate[]> {
    let needsUpdate: Record<string, TokenInfo> = {}

    if (updateInterval) {
      for (const token of tokens) {
        const rate = await this.tokensService.getStoredRateById(token.id)

        if (!rate || !rate?.updatedAt || Date.now() - new Date(rate.updatedAt).getTime() >= updateInterval) {
          needsUpdate[token.cmcId] = token
        }
      }
    } else {
      needsUpdate = keyBy(tokens, (token) => token.cmcId)
    }

    const needsUpdateArr = Object.values(needsUpdate)
    const chunks = splitIntoChunks(needsUpdateArr, BATCH_SIZE)
    this.logger.log(
      `Update rates for ${needsUpdateArr.length} regular tokens. Chunks count: ${chunks.length}. Total tokens: ${tokens.length}`,
    )

    const ratesToSave: Rate[] = []

    for (const chunk of chunks) {
      try {
        const cmcIds = chunk.map((token) => token.cmcId)
        const rates = await this.cmcClient.getHistoricalRates(cmcIds)
        this.logger.log(`Got rates for cmcIds: ${cmcIds.join(',')}`)

        cmcIds.forEach((cmcId) => {
          try {
            const token = needsUpdate[cmcId]
            const rate = rates[cmcId]

            if (!token || !rate) {
              this.logger.warn(`Rate for token ${token.id} is missing. CMC id ${cmcId}`)

              return
            }

            ratesToSave.push({ ...rate, tokenId: token.id })
          } catch (e) {
            this.logger.error(e)
          }
        })
      } catch (e) {
        this.logger.error(e)
      }
    }

    this.logger.log('Regular tokens rates update completed successfully')

    return ratesToSave
  }

  /**
   * Update launchpad tokens using indexer
   */
  private async fetchLaunchpadTokenRates(launchpadTokens: TokenInfo[], updateInterval?: number): Promise<Rate[]> {
    if (launchpadTokens.length === 0) {
      return []
    }

    try {
      this.logger.log(`Updating prices for ${launchpadTokens.length} launchpad tokens using indexer`)

      // Filter tokens that need update if interval is specified
      let tokensToUpdate = launchpadTokens

      if (updateInterval) {
        tokensToUpdate = []
        for (const token of launchpadTokens) {
          const rate = await this.tokensService.getStoredRateById(token.id)

          if (!rate || !rate?.updatedAt || Date.now() - new Date(rate.updatedAt).getTime() >= updateInterval) {
            tokensToUpdate.push(token)
          }
        }
      }

      if (tokensToUpdate.length === 0) {
        this.logger.log('No launchpad tokens need updating')

        return []
      }

      // Prepare token requests for indexer
      const tokenRequests = tokensToUpdate.flatMap((token) =>
        token.chains.map((chain) => ({
          tokenId: token.id,
          chainId: chain.id,
          poolAddress: chain.address, // Use the token's contract address as pool address
        })),
      )

      // Fetch prices from indexer
      const indexerPrices = await this.priceIndexerService.getTokenPrices(tokenRequests)

      // Convert indexer responses to Rate objects
      const rates: Rate[] = []

      for (const token of tokensToUpdate) {
        // Find price for this token (use the first available chain price)
        const tokenPrice = indexerPrices.find((price) => price.token_id === token.id)

        if (tokenPrice) {
          const rate: Rate = {
            tokenId: token.id,
            rate: tokenPrice.price_usd.toString(),
            percentChange: '0', // Indexer doesn't provide percent change yet
            rateHistory: [], // TODO: Implement rate history from indexer
          }

          rates.push(rate)
        } else {
          // Use TGE price as fallback for pre-launch tokens
          const tgePrice = await this.getTgePriceForToken(token.id)

          if (tgePrice) {
            const rate: Rate = {
              tokenId: token.id,
              rate: tgePrice.toString(),
              percentChange: '0',
              rateHistory: [],
            }

            rates.push(rate)
            this.logger.log(`Using TGE price ${tgePrice} for pre-launch token ${token.id}`)
          } else {
            this.logger.warn(`No price found for launchpad token ${token.id}`)
          }
        }
      }

      this.logger.log(`Updated ${rates.length} launchpad token prices from indexer`)

      return rates
    } catch (error) {
      this.logger.error('Failed to update launchpad token prices:', error)
      throw error
    }
  }

  /**
   * Get TGE price for a token from project data (fallback for pre-launch tokens)
   */
  private async getTgePriceForToken(tokenId: string): Promise<number | null> {
    try {
      const projectResult = await this.db
        .select({
          tgePriceUsdc: projects.tgePriceUsdc,
        })
        .from(projects)
        .where(eq(projects.tokenId, tokenId))
        .limit(1)

      if (projectResult.length === 0) {
        this.logger.debug(`No project found for token ${tokenId}`)

        return null
      }

      const tgePrice = projectResult[0].tgePriceUsdc

      if (!tgePrice) {
        this.logger.debug(`No TGE price set for token ${tokenId}`)

        return null
      }

      return parseFloat(tgePrice)
    } catch (error) {
      this.logger.error(`Failed to get TGE price for token ${tokenId}:`, error)

      return null
    }
  }
}

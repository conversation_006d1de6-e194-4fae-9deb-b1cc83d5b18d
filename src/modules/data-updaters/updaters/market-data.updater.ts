import { Injectable, Logger } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

import { type MarketData, TokenInfo, TokensService } from '@/modules/tokens'
import { keyBy } from '@/utils/arrays/key-by'
import { splitIntoChunks } from '@/utils/arrays/split-into-chunks'

import { CmcClient } from '../clients/cmc'
import { BATCH_SIZE } from '../data-updaters.constants'

@Injectable()
export class MarketDataUpdater {
  private logger = new Logger('MarketDataUpdater')

  constructor(
    private tokensService: TokensService,
    private cmcClient: CmcClient,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    waitForCompletion: true,
    name: 'updateMarketData',
    disabled: process.env.NODE_ENV !== 'production',
  })
  private async updateMarketData(): Promise<void> {
    const tokens = await this.tokensService.getTokens({})
    const tokensToUpdate = tokens.filter((token) => !token.isViewOnly)
    const tokensToUpdateObj: Record<string, TokenInfo> = keyBy(tokensToUpdate, (t) => t.cmcId)

    const chunks = splitIntoChunks(tokensToUpdate, BATCH_SIZE)
    this.logger.log(
      `Update market data for ${tokensToUpdate.length} tokens. Chunks count: ${chunks.length}. Total tokens: ${tokens.length}`,
    )

    for (const chunk of chunks) {
      try {
        const cmcIds = chunk.map((token) => token.cmcId)
        const marketDataResponse = await this.cmcClient.getMarketData(cmcIds)
        this.logger.log(`Got market data for cmcIds: ${cmcIds.join(',')}`)

        const marketDataToSave: (MarketData & { tokenId: string })[] = []

        cmcIds.forEach((cmcId) => {
          try {
            const token = tokensToUpdateObj[cmcId]
            const newMarketData = marketDataResponse[cmcId]

            if (!token || !marketDataResponse) {
              this.logger.warn(`Rate for token ${token.id} is missing. CMC id ${cmcId}`)

              return
            }

            const updatedMarketData = { tokenId: token.id, ...token.marketData, ...newMarketData }

            marketDataToSave.push(updatedMarketData)
          } catch (e) {
            this.logger.error(e)
          }
        })

        await this.tokensService.updateMarketData(marketDataToSave)
      } catch (e) {
        this.logger.error(e)
      }
    }

    this.logger.log('Tokens market data update completed successfully')
  }
}

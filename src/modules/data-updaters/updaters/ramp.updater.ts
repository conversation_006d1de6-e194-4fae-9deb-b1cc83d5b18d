import { Injectable, Logger } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

import { ChainsService } from '@/modules/chains'
import { RampService } from '@/modules/ramp'
import { TokensService } from '@/modules/tokens'

import { CashRampClient } from '../clients/cashramp'
import { MoonPayClient } from '../clients/moon-pay'
import { OnrampMoneyClient } from '../clients/onramp-money'
import { PixPayClient } from '../clients/pix-pay'
import { YellowCardClient } from '../clients/yellow-card'

@Injectable()
export class RampUpdater {
  private logger = new Logger('RampUpdater')

  constructor(
    private tokensService: TokensService,
    private chainsService: ChainsService,
    private rampService: RampService,

    private cashRampClient: CashRampClient,
    private moonPayClient: MoonPayClient,
    private onrampMoneyClient: OnrampMoneyClient,
    private pixPayClient: PixPayClient,
    private yellowCardClient: YellowCardClient,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_2AM, {
    waitForCompletion: true,
    name: 'cronUpdateAllOffers',
    disabled: process.env.NODE_ENV !== 'production',
  })
  private async cronUpdateAllOffers() {
    await this.updateAllOffers()
  }

  async updateAllOffers(): Promise<void> {
    try {
      const tokens = await this.tokensService.getTokens({})
      const countries = await this.rampService.getCountries()
      const chains = await this.chainsService.getChains()

      const offers = await Promise.all([
        this.cashRampClient.getRampData(tokens, countries),
        this.moonPayClient.getRampData(tokens, chains, countries),
        this.onrampMoneyClient.getRampData(tokens, countries),
        this.pixPayClient.getRampData(tokens, chains),
        await this.yellowCardClient.getRampData(tokens, chains),
      ])

      await this.rampService.setNewOffers(offers.flat())
    } catch (error) {
      this.logger.error(error)
    }
  }
}

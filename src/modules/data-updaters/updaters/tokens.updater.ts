import { Injectable, Logger } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

import { DiscoverService } from '@/modules/discover'
import { EarnService } from '@/modules/earn'
import { TokensService } from '@/modules/tokens'
import { splitIntoChunks } from '@/utils/arrays/split-into-chunks'

@Injectable()
export class TokensUpdater {
  private logger = new Logger('TokensUpdater')

  constructor(
    private tokensService: TokensService,
    private discoverService: DiscoverService,
    private earnService: EarnService,
  ) {}

  @Cron(CronExpression.EVERY_5_HOURS, {
    waitForCompletion: true,
    name: 'cronDeleteDisabledTokens',
    disabled: process.env.NODE_ENV !== 'production',
  })
  private async cronDeleteDisabledTokens(): Promise<void> {
    await this.deleteDisabledTokens()
  }

  async disableUnusedViewOnlyTokens(): Promise<void> {
    const tokens = await this.tokensService.getTokens({})
    const sections = await this.discoverService.getDiscoverSections()
    const { protocols } = await this.earnService.getEarnData()
    const viewOnlyTokens = tokens.filter((token) => token.isViewOnly)

    const tokensUsedInDiscoveryAndEarn = new Set<string>()

    sections.forEach((section) => {
      if (section.type !== 'tokensListHorizontal' && section.type !== 'tokensListVertical') return

      section.tokens.forEach((t) => tokensUsedInDiscoveryAndEarn.add(t.id))
    })

    protocols.forEach((protocol) => {
      protocol.tokens.forEach((t) => {
        tokensUsedInDiscoveryAndEarn.add(t.tokenId)
        tokensUsedInDiscoveryAndEarn.add(t.returnTokenId)
      })
    })

    const tokensToDelete = viewOnlyTokens.filter((token) => !tokensUsedInDiscoveryAndEarn.has(token.id))
    const ids = tokensToDelete.map((token) => token.id)

    const chunks = splitIntoChunks(ids)

    for (const chunk of chunks) {
      try {
        await this.tokensService.disableTokens(chunk)
      } catch (e) {
        this.logger.error(`Error disabling tokens chunk. Ids: ${chunk.join(',')}`, e)
      }
    }

    this.logger.log(`Disable ${tokensToDelete.length} unused tokens`)
  }

  async deleteDisabledTokens(): Promise<void> {
    const tokenIds = await this.tokensService.getDisabledTokens()
    const chunks = splitIntoChunks(tokenIds)

    for (const chunk of chunks) {
      try {
        await this.tokensService.deleteTokens(chunk)
      } catch (e) {
        this.logger.error(`Error deleting tokens chunk. Ids: ${chunk.join(',')}`, e)
      }
    }

    this.logger.log(`Deleted ${tokenIds.length} disabled tokens`)
  }
}

import { z } from 'zod'

import { marketDataSchema } from './market-data.schema'
import { tokenChainInfoSchema } from './token-chain-info.schema'

export const tokenInfoSchema = z.object({
  id: z.string(),
  ticker: z.string(),
  fullName: z.string(),
  cmcId: z.string(),
  ccId: z.string().optional(),
  coingeckoId: z.string().optional(),
  imageUrl: z.string(),
  chains: z.array(tokenChainInfoSchema),
  isStable: z.boolean(),
  isViewOnly: z.boolean(),
  isDefault: z.boolean(),
  isTradable: z.boolean(),
  isLaunchpadToken: z.boolean(),
  marketData: marketDataSchema,
  displayInfo: z.object({
    ticker: z.string(),
    isChartHidden: z.boolean(),
    subtitle: z.string().optional(),
    isAvailableForOnramp: z.boolean().optional(),
  }),
})

export type TokenInfo = z.infer<typeof tokenInfoSchema>

import { z } from 'zod'

export const marketDataSchema = z.object({
  tokenRank: z.number(),
  description: z.string(),
  categories: z.array(z.string()),
  fdv: z.number().optional(),
  launchDate: z.string().optional(),
  totalSupply: z.number().optional(),
  circulatingSupply: z.number().optional(),
  liquidityDepth: z.enum(['excellent', 'good', 'moderate', 'low', 'average', 'poor']).optional(),
  tradingVolume24h: z.number().optional(),
  securityRating: z.enum(['no-issues', 'dangerous', 'no-info']).optional(),
  links: z
    .object({
      website: z.string().optional(),
      twitter: z.string().optional(),
      telergam: z.string().optional(),
      discord: z.string().optional(),
      warpcast: z.string().optional(),
      instagram: z.string().optional(),
    })
    .optional(),
})

export type MarketData = z.infer<typeof marketDataSchema>

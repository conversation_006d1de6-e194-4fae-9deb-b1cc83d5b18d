import { Controller, Get, HttpException, Logger, Param, Query } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { GetRateByIdDto } from './dto/request/get-rate-by-id.dto'
import { GetRatesRequestDto } from './dto/request/get-rates.dto'
import { GetTokenByContractDto } from './dto/request/get-token-by-contract.dto'
import { GetTokenByIdDto } from './dto/request/get-token-by-id.dto'
import { GetTokensRequestDto } from './dto/request/get-tokens.dto'
import { GetRateByIdResponseDto } from './dto/response/get-rate-by-id.dto'
import { GetRatesResponseDto } from './dto/response/get-rates.dto'
import { GetTokenByIdResponseDto } from './dto/response/get-token-by-id.dto'
import { GetTokensResponseDto } from './dto/response/get-tokens.dto'
import { TokensService } from './tokens.service'

@ApiTags('Tokens')
@Controller({
  path: '/tokens',
  version: '1',
})
export class TokensController {
  private logger = new Logger('TokensController')

  constructor(private readonly tokensService: TokensService) {}

  @ApiOperation({ description: 'Get all tokens data' })
  @ApiOkResponse({ type: GetTokensResponseDto })
  @Get('/')
  async getTokensList(@Query() queryParams?: GetTokensRequestDto): Promise<GetTokensResponseDto> {
    this.logger.log(`Get all tokens data with params ${JSON.stringify(queryParams)}`)
    const tokens = await this.tokensService.getTokens({
      isTradable: queryParams?.isTradable,
      isLaunchpadToken: queryParams?.isLaunchpadToken,
    })

    return { success: true, total: tokens.length, data: tokens }
  }

  @ApiOperation({ description: 'Get all tokens rates' })
  @ApiOkResponse({ type: GetTokensResponseDto })
  @Get('/rates')
  async getTokensRates(@Query() queryParams?: GetRatesRequestDto): Promise<GetRatesResponseDto> {
    this.logger.log(`Get all tokens rates data with params ${JSON.stringify(queryParams)}`)
    const rates = await this.tokensService.getRates({
      isTradable: queryParams?.isTradable,
      includeHistory: queryParams?.includeHistory,
      isLaunchpadToken: queryParams?.isLaunchpadToken,
    })

    return { success: true, total: rates.length, data: rates }
  }

  @ApiOperation({ description: 'Get token rate by token id' })
  @ApiOkResponse({ type: GetRateByIdResponseDto })
  @Get('/rates/:id')
  async getTokenRate(@Param() params: GetRateByIdDto): Promise<GetRateByIdResponseDto> {
    const { id } = params
    this.logger.log(`Get token rate data by id: ${id}`)
    const rate = await this.tokensService.getRateById(id)

    if (!rate) {
      throw new HttpException({ success: false }, 404)
    }

    return { success: true, data: rate }
  }

  @ApiOperation({ description: 'Get token data by id' })
  @ApiOkResponse({ type: GetTokenByIdResponseDto })
  @Get('/token-by-contract')
  async getTokenByContract(@Query() params: GetTokenByContractDto): Promise<GetTokenByIdResponseDto> {
    const { chainId, contract } = params
    this.logger.log(`Get token by contract: ${contract}, chainId: ${chainId}`)
    const token = await this.tokensService.getTokenByContract(contract, chainId)

    if (!token) {
      throw new HttpException({ success: false }, 404)
    }

    return { success: true, data: token }
  }

  @ApiOperation({ description: 'Get token data by id' })
  @ApiOkResponse({ type: GetTokenByIdResponseDto })
  @Get(':id')
  async getTokenById(@Param() params: GetTokenByIdDto): Promise<GetTokenByIdResponseDto> {
    const { id } = params
    this.logger.log(`Get token by id: ${id}`)
    const token = await this.tokensService.getTokenById(id)

    if (!token) {
      throw new HttpException({ success: false }, 404)
    }

    return { success: true, data: token }
  }
}

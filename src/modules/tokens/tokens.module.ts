import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'

import { IndexerModule } from '@/libs/indexer'

import { TokensStorage } from './storage'
import { TokensController } from './tokens.controller'
import { TokensService } from './tokens.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { ChainsModule } from '../chains'
import { FirebaseModule } from '../firebase/firebase.module'

@Module({
  imports: [FirebaseModule, ChainsModule, ScheduleModule.forRoot(), DatabaseModule, IndexerModule],
  controllers: [TokensController],
  providers: [TokensService, TokensStorage],
  exports: [TokensService, TokensStorage],
})
export class TokensModule {}

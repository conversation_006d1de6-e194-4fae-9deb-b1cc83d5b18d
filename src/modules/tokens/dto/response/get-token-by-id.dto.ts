import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean } from 'class-validator'

import { TokenInfoDto } from '../common/token-info.dto'

export class GetTokenByIdResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({
    description: 'Array of tokens',
    example: {
      id: 'pfTQK6FYmt7AlHXjyC9S',
      ticker: 'BTC',
      fullName: 'Bitcoin',
      coingeckoId: 'bitcoin',
      imageUrl: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1.png',
      chains: [
        {
          id: 'ethereum',
          address: '******************************************',
          decimals: 18,
          isNative: false,
        },
      ],
      isStable: false,
      isViewOnly: false,
      isDefault: true,
      isTradable: true,
      marketData: {
        tokenRank: 1,
        description: 'Bitcoin is the first successful ...',
        categories: [],
        fdv: 1726811709371,
        launchDate: '2009-01-03',
        totalSupply: 19839190,
        circulatingSupply: 19839190,
        liquidityDepth: 'excellent',
        tradingVolume24h: 31709943252,
        links: {
          website: 'http://www.bitcoin.org',
          twitter: 'https://twitter.com/bitcoin',
        },
      },
      rateData: {
        rate: '97970.39174109632',
        rateHistory: [
          {
            value: 97036.38198944656,
            timestamp: 1735840800000,
          },
        ],
        percentChange: '0.96',
      },
      displayInfo: {
        ticker: 'BTC',
        isChartHidden: false,
        isAvailableForOnramp: false,
      },
    },
    required: true,
  })
  @Type(() => TokenInfoDto)
  data: TokenInfoDto
}

import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsBoolean, IsNumber, ValidateNested } from 'class-validator'

import { RateDto } from '../common/rate.dto'

export class GetRatesResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({ description: 'Total amount of tokens rates', example: 1 })
  @IsNumber()
  total: number

  @ApiProperty({
    description: 'Array of tokens',
    example: [
      {
        rate: '97970.39174109632',
        rateHistory: [
          {
            value: 97036.38198944656,
            timestamp: 1735840800000,
          },
          {
            value: 97036.38198944656,
            timestamp: 1735840800000,
          },
          {
            value: 97036.38198944656,
            timestamp: 1735840800000,
          },
        ],
        percentChange: '0.96',
      },
    ],
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RateDto)
  data: RateDto[]
}

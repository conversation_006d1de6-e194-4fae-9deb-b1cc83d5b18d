import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean } from 'class-validator'

import { RateDto } from '../common/rate.dto'

export class GetRateByIdResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({
    description: "Token's rate information",
    example: {
      rate: '97970.39174109632',
      rateHistory: [
        {
          value: 97036.38198944656,
          timestamp: 1735840800000,
        },
        {
          value: 97036.38198944656,
          timestamp: 1735840800000,
        },
        {
          value: 97036.38198944656,
          timestamp: 1735840800000,
        },
      ],
      percentChange: '0.96',
    },
    required: true,
  })
  @Type(() => RateDto)
  data: RateDto
}

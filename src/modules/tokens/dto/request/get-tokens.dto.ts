import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsBoolean, IsOptional } from 'class-validator'

export class GetTokensRequestDto {
  @ApiProperty({
    description: 'Flag to receive only available for trade tokens',
    example: true,
    required: false,
    type: <PERSON>olean,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true

    if (value === 'false') return false

    return value
  })
  isTradable: boolean

  @ApiProperty({
    description: 'Flag to receive only launchpad tokens',
    example: true,
    required: false,
    type: Boolean,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true

    if (value === 'false') return false

    return value
  })
  isLaunchpadToken: boolean
}

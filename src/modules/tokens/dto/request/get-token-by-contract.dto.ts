import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsNumber, IsString } from 'class-validator'

export class GetTokenByContractDto {
  @ApiProperty({ name: 'chainId', description: 'Chain ID', type: Number })
  @IsNumber()
  @Transform(({ value }) => {
    const number = Number(value)

    if (!Number.isNaN(number)) return number

    return value
  })
  chainId: number

  @ApiProperty({ name: 'contract', description: 'Contract Address' })
  @IsString()
  contract: string
}

import { Big } from '@/libs/big-number'
import { deleteUndefinedFields } from '@/utils/objects/delete-undefined-fields'

import { type TokenInfoFromStorage, tokenInfoFromStorageSchema } from './tokens-storage.schema'
import type { Rate } from '../schemas/rate.schema'
import type { TokenInfo } from '../schemas/token-info.schema'

type MappedTokenInfo = { tokenInfo: TokenInfo; rate: StoredRate }

export type StoredRate = Rate & {
  isTradable: boolean
  updatedAt?: string
}

export const mapTokenInfo = (saved: TokenInfoFromStorage): MappedTokenInfo => {
  const savedMarketData = saved.marketData
  const isTradable = saved.isTradable || false
  const ticker =
    typeof saved.displayOptions?.displayTicker === 'string' ? saved.displayOptions.displayTicker : saved.ticker

  const tokenInfo: TokenInfo = {
    id: saved.id,
    ticker: saved.ticker,
    fullName: saved.fullName,
    cmcId: String(saved.cmcId),
    ccId: saved.ccId ? String(saved.ccId) : undefined,
    coingeckoId: saved.coingeckoId ? String(saved.coingeckoId) : undefined,
    imageUrl: saved.imageUrl,
    chains: (saved.chains || []).map((chain) => ({
      id: chain.id,
      address: chain.address,
      decimals: chain.decimals,
      isNative: !!chain.isNative,
    })),
    isStable: saved.isStable || false,
    isViewOnly: saved.isViewOnly || false,
    isDefault: saved.isDefault || false,
    isTradable,
    isLaunchpadToken: saved.isLaunchpadToken || false,
    marketData: {
      tokenRank: savedMarketData.tokenRank || 999999,
      description: saved.description || '',
      categories: saved.categories || [],
      fdv: savedMarketData.fdv,
      launchDate: saved.launchDate ?? undefined,
      totalSupply: savedMarketData.totalSupply,
      circulatingSupply: savedMarketData.circulatingSupply,
      liquidityDepth: savedMarketData.liquidityDepth,
      tradingVolume24h: savedMarketData.tradingVolume24h,
      securityRating: savedMarketData.securityRating,
      links: saved.links,
    },
    displayInfo: {
      ticker,
      isChartHidden: !!saved.displayOptions?.isChartHidden,
      subtitle: typeof saved.displayOptions?.subtitle === 'string' ? saved.displayOptions?.subtitle : undefined,
      isAvailableForOnramp: !!saved.displayOptions?.isAvailableForOnramp,
    },
  }

  const currentRate = Big(saved.rateData?.rate || 0)
  const percentChange = Big(saved.rateData?.percentChange || 0)
  const history = saved.rateData?.['1d'] || []
  const rate: StoredRate = {
    isTradable,
    tokenId: saved.id,
    rate: !currentRate.isNaN() ? currentRate.toString() : '0',
    rateHistory: history.map((point) => ({ value: point.rate, timestamp: point.timestamp })),
    percentChange: !percentChange.isNaN() ? percentChange.toString() : '0',
    updatedAt: saved.rateData?.updatedAt,
  }

  return { tokenInfo, rate }
}

export const unmapTokenInfo = (tokenInfo: TokenInfo, rate: StoredRate): TokenInfoFromStorage => {
  const marketData: TokenInfoFromStorage['marketData'] = {
    tokenRank: tokenInfo.marketData.tokenRank,
    fdv: tokenInfo.marketData.fdv,
    totalSupply: tokenInfo.marketData.totalSupply,
    circulatingSupply: tokenInfo.marketData.circulatingSupply,
    liquidityDepth: tokenInfo.marketData.liquidityDepth,
    tradingVolume24h: tokenInfo.marketData.tradingVolume24h,
    securityRating: tokenInfo.marketData.securityRating,
  }

  const rateData: TokenInfoFromStorage['rateData'] = {
    rate: rate.rate,
    percentChange: rate.percentChange,
    '1d': rate.rateHistory?.map((r) => ({ rate: r.value, timestamp: r.timestamp })),
    updatedAt: rate.updatedAt!,
  }

  const displayOptions: TokenInfoFromStorage['displayOptions'] = {
    displayTicker: tokenInfo.displayInfo.ticker,
    isChartHidden: tokenInfo.displayInfo.isChartHidden,
    subtitle: tokenInfo.displayInfo.subtitle,
    isAvailableForOnramp: tokenInfo.displayInfo.isAvailableForOnramp,
  }

  const links: TokenInfoFromStorage['links'] = {
    website: tokenInfo.marketData.links?.website,
    twitter: tokenInfo.marketData.links?.twitter,
    telergam: tokenInfo.marketData.links?.telergam,
    discord: tokenInfo.marketData.links?.discord,
    warpcast: tokenInfo.marketData.links?.warpcast,
    instagram: tokenInfo.marketData.links?.instagram,
  }

  return tokenInfoFromStorageSchema.parse(
    deleteUndefinedFields({
      marketData: deleteUndefinedFields(marketData),
      rateData: deleteUndefinedFields(rateData),
      links: deleteUndefinedFields(links),
      displayOptions: deleteUndefinedFields(displayOptions),

      id: tokenInfo.id,
      ticker: tokenInfo.ticker,
      fullName: tokenInfo.fullName,
      cmcId: tokenInfo.cmcId,
      ccId: tokenInfo.ccId,
      coingeckoId: tokenInfo.coingeckoId,
      imageUrl: tokenInfo.imageUrl,
      chains: tokenInfo.chains,
      isStable: tokenInfo.isStable,
      isViewOnly: tokenInfo.isViewOnly,
      isTradable: tokenInfo.isTradable,
      categories: tokenInfo.marketData.categories,
      isDefault: tokenInfo.isDefault,
      description: tokenInfo.marketData.description,
      launchDate: tokenInfo.marketData.launchDate,
      isLaunchpadToken: tokenInfo.isLaunchpadToken,
    }),
  )
}

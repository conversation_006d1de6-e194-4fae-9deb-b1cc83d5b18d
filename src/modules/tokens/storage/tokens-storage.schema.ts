import { z } from 'zod'

export const marketDataFromStorageSchema = z.object({
  tokenRank: z.number(),
  fdv: z.number().optional(),
  totalSupply: z.number().optional(),
  circulatingSupply: z.number().optional(),
  liquidityDepth: z.enum(['excellent', 'good', 'moderate', 'low', 'average', 'poor']).optional(),
  tradingVolume24h: z.number().optional(),
  securityRating: z.enum(['no-issues', 'dangerous', 'no-info']).optional(),
})

export const rateFromStorageSchema = z.object({
  rate: z.union([z.string(), z.number()]),
  '1d': z.array(z.object({ rate: z.number(), timestamp: z.number() })).optional(),
  percentChange: z.union([z.string(), z.number()]),
  updatedAt: z.string(),
})

export const tokenInfoFromStorageSchema = z.object({
  id: z.string(),
  ticker: z.string(),
  fullName: z.string(),
  cmcId: z.union([z.string(), z.number()]),
  ccId: z.union([z.string(), z.number()]).optional().nullable(),
  coingeckoId: z.union([z.string(), z.number()]).optional().nullable(),
  imageUrl: z.string(),

  chains: z.array(
    z.object({
      id: z.string(),
      address: z.string(),
      decimals: z.number(),
      isNative: z.boolean().optional(),
    }),
  ),

  isStable: z.boolean(),
  isViewOnly: z.boolean(),
  isTradable: z.boolean().optional(),
  isDisabled: z.boolean().optional(),
  isLaunchpadToken: z.boolean().optional(),

  marketData: marketDataFromStorageSchema,

  categories: z.array(z.string()),
  isDefault: z.boolean().optional(),
  description: z.string().optional(),

  rateData: rateFromStorageSchema.optional(),

  launchDate: z.string().optional().nullable(),

  links: z
    .object({
      website: z.string().optional(),
      twitter: z.string().optional(),
      telergam: z.string().optional(),
      discord: z.string().optional(),
      warpcast: z.string().optional(),
      instagram: z.string().optional(),
    })
    .optional(),

  displayOptions: z
    .object({
      displayTicker: z.string().optional(),
      isChartHidden: z.boolean().optional(),
      subtitle: z.string().optional(),
      isAvailableForOnramp: z.boolean().optional(),
    })
    .optional(),
})

export type TokenInfoFromStorage = z.infer<typeof tokenInfoFromStorageSchema>
export type MarketDataFromStorage = z.infer<typeof marketDataFromStorageSchema>

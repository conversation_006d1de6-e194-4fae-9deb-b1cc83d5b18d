import { Injectable } from '@nestjs/common'

import { type MarketData } from './schemas/market-data.schema'
import { type Rate } from './schemas/rate.schema'
import { type TokenInfo } from './schemas/token-info.schema'
import { TokensStorage, type StoredRate, type TokenToCreate } from './storage'
import { ChainsService } from '../chains'

@Injectable()
export class TokensService {
  constructor(
    private storage: TokensStorage,
    private chainsService: ChainsService,
  ) {}

  /*
   * Getters
   */
  async getTokens(params: { isTradable?: boolean; isLaunchpadToken?: boolean }): Promise<TokenInfo[]> {
    const tokens = await this.storage.getTokens(params)

    if (!tokens.length) throw new Error('Got empty list from tokensService')

    return tokens
  }

  async getTokenById(id: string): Promise<TokenInfo | undefined> {
    return this.storage.getTokenById(id)
  }

  async getTokenByContract(contract: string, nativeChainId: number): Promise<TokenInfo | undefined> {
    const chainInfo = await this.chainsService.getChainByChainId(nativeChainId)

    if (!chainInfo) return undefined

    const tokens = await this.storage.getTokens({ isTradable: true })

    return tokens.find((cur) => {
      const inChain = cur.chains.find((c) => c.id === chainInfo.id)

      return inChain?.address?.toLowerCase() === contract.toLowerCase()
    })
  }

  async getRates(params: {
    isTradable?: boolean
    includeHistory?: boolean
    isLaunchpadToken?: boolean
  }): Promise<Rate[]> {
    const rates = await this.storage.getRates(params)

    if (!rates.length) throw new Error('Got empty list from tokensService')

    return rates
  }

  async getRateById(id: string): Promise<Rate | undefined> {
    return this.storage.getRateById(id)
  }

  async getStoredRateById(id: string): Promise<StoredRate | undefined> {
    return this.storage.getStoredRateById(id)
  }

  async getDisabledTokens(): Promise<string[]> {
    return this.storage.getDisabledTokens()
  }

  /*
   * Setters
   */
  async updateTokens(tokenInfo: TokenInfo[], skipCacheRevalidation?: boolean): Promise<void> {
    await this.storage.updateTokens(tokenInfo, skipCacheRevalidation)
  }

  async addTokens(tokens: TokenToCreate[], skipCacheRevalidation?: boolean): Promise<TokenInfo[]> {
    return this.storage.addTokens(tokens, skipCacheRevalidation)
  }

  async updateMarketData(marketData: (MarketData & { tokenId: string })[]): Promise<void> {
    await this.storage.updateMarketData(marketData)
  }

  async updateRates(rates: Rate[]): Promise<void> {
    await this.storage.updateRates(rates)
  }

  async deleteTokens(ids: string[]): Promise<void> {
    await this.storage.deleteTokens(ids)
  }

  async disableTokens(ids: string[]): Promise<void> {
    await this.storage.disableTokens(ids)
  }
}

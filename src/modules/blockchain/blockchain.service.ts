import { randomUUID } from 'crypto'

import { Injectable, Lo<PERSON>, OnModuleInit } from '@nestjs/common'
import { getAssociatedTokenAddressSync, createTransferInstruction, TOKEN_PROGRAM_ID } from '@solana/spl-token'
import { Connection, PublicKey, Keypair, Transaction } from '@solana/web3.js'
import bs58 from 'bs58'
import {
  createPublicClient,
  createWalletClient,
  http,
  Address,
  getContract,
  formatUnits,
  PublicClient,
  WalletClient,
} from 'viem'
import { privateKeyToAccount } from 'viem/accounts'
import { mainnet, arbitrum, base } from 'viem/chains'

import { getEnvConfig } from '@/config/env'

// Extended ERC20 ABI with permit and transferWithAuthorization functions
const erc20WithPermitAbi = [
  // Standard ERC20 functions
  {
    type: 'function',
    name: 'balanceOf',
    inputs: [{ name: 'account', type: 'address' }],
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
  },
  {
    type: 'function',
    name: 'transfer',
    inputs: [
      { name: 'to', type: 'address' },
      { name: 'amount', type: 'uint256' },
    ],
    outputs: [{ name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'approve',
    inputs: [
      { name: 'spender', type: 'address' },
      { name: 'amount', type: 'uint256' },
    ],
    outputs: [{ name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'allowance',
    inputs: [
      { name: 'owner', type: 'address' },
      { name: 'spender', type: 'address' },
    ],
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
  },
  // Permit function (EIP-2612)
  {
    type: 'function',
    name: 'permit',
    inputs: [
      { name: 'owner', type: 'address' },
      { name: 'spender', type: 'address' },
      { name: 'value', type: 'uint256' },
      { name: 'deadline', type: 'uint256' },
      { name: 'v', type: 'uint8' },
      { name: 'r', type: 'bytes32' },
      { name: 's', type: 'bytes32' },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'nonces',
    inputs: [{ name: 'owner', type: 'address' }],
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
  },
  // Transfer with authorization (EIP-3009)
  {
    type: 'function',
    name: 'transferWithAuthorization',
    inputs: [
      { name: 'from', type: 'address' },
      { name: 'to', type: 'address' },
      { name: 'value', type: 'uint256' },
      { name: 'validAfter', type: 'uint256' },
      { name: 'validBefore', type: 'uint256' },
      { name: 'nonce', type: 'bytes32' },
      { name: 'v', type: 'uint8' },
      { name: 'r', type: 'bytes32' },
      { name: 's', type: 'bytes32' },
    ],
    outputs: [],
    stateMutability: 'nonpayable',
  },
  {
    type: 'function',
    name: 'authorizationState',
    inputs: [
      { name: 'authorizer', type: 'address' },
      { name: 'nonce', type: 'bytes32' },
    ],
    outputs: [{ name: '', type: 'bool' }],
    stateMutability: 'view',
  },
] as const

// USDC contract addresses (properly checksummed)
export const USDC_CONTRACTS = {
  mainnet: '******************************************' as Address, // Official USDC on Ethereum mainnet
  arbitrum: '******************************************' as Address, // USDC on Arbitrum
  base: '******************************************' as Address, // USDC on Base
} as const

// Domain parameters for EIP-3009 transferWithAuthorization
export const USDC_TRANSFER_AUTH_DOMAIN_CONFIGS = {
  mainnet: {
    name: 'USD Coin',
    version: '2',
  },
  arbitrum: {
    name: 'USD Coin (Arb1)',
    version: '1',
  },
  base: {
    name: 'USD Coin',
    version: '2',
  },
} as const

export type SupportedChain = keyof typeof USDC_CONTRACTS

export enum WalletType {
  SWEEP = 'sweep',
  POOL_MANAGER = 'pool_manager',
}

export interface ChainClient {
  publicClient: PublicClient
  chainName: SupportedChain
  chainId: number
  rpcUrl: string
}

export interface ManagedWalletClient {
  client: WalletClient
  address: Address
}

@Injectable()
export class BlockchainService implements OnModuleInit {
  private readonly logger = new Logger(BlockchainService.name)
  private readonly envConfig = getEnvConfig()

  // Persistent clients
  private solanaConnection!: Connection
  private evmClients: Record<SupportedChain, ChainClient> = {} as Record<SupportedChain, ChainClient>
  private walletClients: Record<SupportedChain, Record<WalletType, ManagedWalletClient>> = {} as Record<
    SupportedChain,
    Record<WalletType, ManagedWalletClient>
  >

  async onModuleInit() {
    await this.initializeClients()
  }

  /**
   * Initialize all blockchain clients
   */
  private async initializeClients() {
    this.logger.log('Initializing blockchain clients...')

    // Initialize Solana connection
    this.initializeSolanaClient()

    // Initialize EVM clients for all supported chains
    this.initializeEvmClients()

    this.logger.log('All blockchain clients initialized successfully')
  }

  /**
   * Initialize Solana client
   */
  private initializeSolanaClient() {
    const rpcUrl = this.envConfig.blockchain.solanaRpcUrl

    if (!rpcUrl) {
      throw new Error('Solana RPC URL not configured')
    }

    this.solanaConnection = new Connection(rpcUrl, 'confirmed')
    this.logger.log(`Solana client initialized with RPC: ${rpcUrl}`)
  }

  /**
   * Initialize EVM clients for all supported chains
   */
  private initializeEvmClients() {
    const chainConfigs = {
      mainnet: {
        chain: mainnet,
        rpcUrl: this.envConfig.blockchain.ethereumRpcUrl,
      },
      arbitrum: {
        chain: arbitrum,
        rpcUrl: this.envConfig.blockchain.arbitrumRpcUrl,
      },
      base: {
        chain: base,
        rpcUrl: this.envConfig.blockchain.baseRpcUrl,
      },
    }

    for (const [chainName, config] of Object.entries(chainConfigs)) {
      const typedChainName = chainName as SupportedChain

      if (!config.rpcUrl) {
        throw new Error(`${chainName} RPC URL not configured`)
      }

      // Create public client
      const publicClient = createPublicClient({
        chain: config.chain,
        transport: http(config.rpcUrl),
      }) as PublicClient

      this.evmClients[typedChainName] = {
        publicClient,
        chainName: typedChainName,
        chainId: config.chain.id,
        rpcUrl: config.rpcUrl,
      }

      // Initialize wallet clients for this chain
      this.walletClients[typedChainName] = {} as Record<WalletType, ManagedWalletClient>
      this.initializeWalletClientsForChain(typedChainName, config)

      this.logger.log(`${chainName} clients initialized with RPC: ${config.rpcUrl}`)
    }
  }

  /**
   * Initialize wallet clients for a specific chain
   */
  private initializeWalletClientsForChain(chainName: SupportedChain, config: any) {
    // Create sweep wallet client
    const sweepAccount = privateKeyToAccount(this.envConfig.wallets.sweep.evmPrivateKey as `0x${string}`)
    const sweepWalletClient = createWalletClient({
      account: sweepAccount,
      chain: config.chain,
      transport: http(config.rpcUrl),
    })

    this.walletClients[chainName][WalletType.SWEEP] = {
      client: sweepWalletClient,
      address: sweepAccount.address,
    }

    // Create pool manager wallet client
    const poolManagerAccount = privateKeyToAccount(this.envConfig.wallets.poolManager.evmPrivateKey as `0x${string}`)
    const poolManagerWalletClient = createWalletClient({
      account: poolManagerAccount,
      chain: config.chain,
      transport: http(config.rpcUrl),
    })

    this.walletClients[chainName][WalletType.POOL_MANAGER] = {
      client: poolManagerWalletClient,
      address: poolManagerAccount.address,
    }

    this.logger.log(
      `Wallet clients initialized for ${chainName}: sweep (${sweepAccount.address}), pool manager (${poolManagerAccount.address})`,
    )
  }

  /**
   * Get public client for a specific chain
   */
  getPublicClient(chainName: SupportedChain): PublicClient {
    const client = this.evmClients[chainName]

    if (!client) {
      throw new Error(`EVM client not found for chain: ${chainName}`)
    }

    return client.publicClient
  }

  /**
   * Get sweep wallet client for a specific chain
   */
  getSweepClient(chainName: SupportedChain): WalletClient {
    const walletClient = this.walletClients[chainName]?.[WalletType.SWEEP]

    if (!walletClient) {
      throw new Error(`Sweep wallet client not found for chain: ${chainName}`)
    }

    return walletClient.client
  }

  /**
   * Get pool manager wallet client for a specific chain
   */
  getPoolManagerClient(chainName: SupportedChain): WalletClient {
    const walletClient = this.walletClients[chainName]?.[WalletType.POOL_MANAGER]

    if (!walletClient) {
      throw new Error(`Pool manager wallet client not found for chain: ${chainName}`)
    }

    return walletClient.client
  }

  /**
   * Get wallet address for a specific wallet type and chain
   */
  getWalletAddress(chainName: SupportedChain, walletType: WalletType): Address {
    const walletClient = this.walletClients[chainName]?.[walletType]

    if (!walletClient) {
      throw new Error(`${walletType} wallet client not found for chain: ${chainName}`)
    }

    return walletClient.address
  }

  /**
   * @deprecated Use getPublicClient, getSweepClient, or getPoolManagerClient instead
   */
  getEvmClient(chainName: SupportedChain): ChainClient & { walletClient: WalletClient } {
    const client = this.evmClients[chainName]

    if (!client) {
      throw new Error(`EVM client not found for chain: ${chainName}`)
    }

    // Return sweep client for backward compatibility
    const sweepClient = this.getSweepClient(chainName)

    return { ...client, walletClient: sweepClient }
  }

  /**
   * Check generic token balance on specific EVM chain
   */
  async checkEvmTokenBalance(chainName: SupportedChain, address: Address, tokenAddress: Address): Promise<number> {
    try {
      const client = this.getPublicClient(chainName)
      const tokenContract = getContract({
        address: tokenAddress,
        abi: erc20WithPermitAbi,
        client,
      })

      const balance = await tokenContract.read.balanceOf([address])
      const balanceFormatted = parseFloat(formatUnits(balance, 6)) // Assuming 6 decimals, should be parameterized for other tokens

      this.logger.log(`Token balance on ${chainName}: ${balanceFormatted}`)

      return balanceFormatted
    } catch (error) {
      this.logger.warn(`Error checking ${chainName} token balance:`, error)

      return 0
    }
  }

  /**
   * Check USDC balance on specific EVM chain
   */
  async checkEvmUsdcBalance(chainName: SupportedChain, address: Address): Promise<number> {
    return this.checkEvmTokenBalance(chainName, address, USDC_CONTRACTS[chainName])
  }

  /**
   * Check USDC balance on all EVM chains
   */
  async checkAllEvmUsdcBalances(address: Address): Promise<number> {
    let totalBalance = 0

    for (const chainName of Object.keys(this.evmClients) as SupportedChain[]) {
      const balance = await this.checkEvmUsdcBalance(chainName, address)
      totalBalance += balance
    }

    return totalBalance
  }

  /**
   * Check generic token balance on Solana
   */
  async checkSolanaTokenBalance(address: string, mintAddress: PublicKey): Promise<number> {
    try {
      const depositTokenAccount = getAssociatedTokenAddressSync(mintAddress, new PublicKey(address))

      const tokenAccountInfo = await this.solanaConnection.getTokenAccountBalance(depositTokenAccount)
      const balance = tokenAccountInfo.value.uiAmount || 0

      this.logger.log(`Token balance on Solana: ${balance}`)

      return balance
    } catch (error) {
      this.logger.warn('Error checking Solana token balance:', error)

      return 0
    }
  }

  /**
   * Check USDC balance on Solana
   */
  async checkSolanaUsdcBalance(address: string): Promise<number> {
    return this.checkSolanaTokenBalance(address, this.getSolanaUsdcMint())
  }

  /**
   * Check all USDC balances (EVM + Solana)
   */
  async checkAllUsdcBalances(
    evmAddress: Address,
    solanaAddress: string,
  ): Promise<{
    total: number
    evm: number
    solana: number
  }> {
    const [evmBalance, solanaBalance] = await Promise.all([
      this.checkAllEvmUsdcBalances(evmAddress),
      this.checkSolanaUsdcBalance(solanaAddress),
    ])

    return {
      total: evmBalance + solanaBalance,
      evm: evmBalance,
      solana: solanaBalance,
    }
  }

  /**
   * Get Solana connection
   */
  getSolanaConnection(): Connection {
    if (!this.solanaConnection) {
      throw new Error('Solana client not initialized')
    }

    return this.solanaConnection
  }

  /**
   * Get all EVM clients (public clients only)
   */
  getAllEvmClients(): Record<SupportedChain, ChainClient> {
    return this.evmClients
  }

  /**
   * Get all sweep wallet clients
   */
  getAllSweepClients(): Record<SupportedChain, WalletClient> {
    const result = {} as Record<SupportedChain, WalletClient>
    for (const chainName of Object.keys(this.evmClients) as SupportedChain[]) {
      result[chainName] = this.getSweepClient(chainName)
    }

    return result
  }

  /**
   * Get USDC mint address for Solana
   */
  getSolanaUsdcMint(): PublicKey {
    return new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')
  }

  /**
   * Get USDC contract address for specific EVM chain
   */
  getUsdcContractAddress(chainName: SupportedChain): Address {
    return USDC_CONTRACTS[chainName]
  }

  /**
   * Get domain configuration for EIP-3009 transferWithAuthorization
   */
  getTransferAuthDomainConfig(chainName: SupportedChain) {
    return USDC_TRANSFER_AUTH_DOMAIN_CONFIGS[chainName]
  }

  /**
   * Get extended ERC20 ABI with permit functions
   */
  getErc20WithPermitAbi() {
    return erc20WithPermitAbi
  }

  /**
   * Health check for all clients
   */
  async healthCheck(): Promise<{ solana: boolean; evm: Record<SupportedChain, boolean> }> {
    const results = {
      solana: false,
      evm: {} as Record<SupportedChain, boolean>,
    }

    // Test Solana connection
    try {
      await this.solanaConnection.getLatestBlockhash()
      results.solana = true
    } catch (error) {
      this.logger.warn('Solana health check failed:', error)
    }

    // Test EVM connections
    for (const [chainName, client] of Object.entries(this.evmClients)) {
      try {
        await client.publicClient.getBlockNumber()
        results.evm[chainName as SupportedChain] = true
      } catch (error) {
        this.logger.warn(`${chainName} health check failed:`, error)
        results.evm[chainName as SupportedChain] = false
      }
    }

    return results
  }

  /**
   * Sweep USDC from specific EVM chain using transferWithAuthorization
   */
  async sweepFromEvmChain(
    chainName: SupportedChain,
    depositAddress: Address,
    depositPrivateKey: string,
    sweepToAddress: Address,
    balance: bigint,
  ): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      const client = this.getEvmClient(chainName)
      const depositAccount = privateKeyToAccount(depositPrivateKey as `0x${string}`)

      // Generate a unique nonce for this authorization (32 bytes)
      const authNonce = `0x${randomUUID().replace(/-/g, '')}${randomUUID().replace(/-/g, '')}`.slice(
        0,
        66,
      ) as `0x${string}`

      // Set validity window (valid for 1 hour)
      const validAfter = BigInt(0) // Valid immediately
      const validBefore = BigInt(Math.floor(Date.now() / 1000) + 3600) // Valid for 1 hour

      // Get domain configuration for transferWithAuthorization
      const domainConfig = this.getTransferAuthDomainConfig(chainName)

      // Create transferWithAuthorization signature (EIP-3009)
      const domain = {
        name: domainConfig.name,
        version: domainConfig.version,
        chainId: client.chainId,
        verifyingContract: USDC_CONTRACTS[chainName],
      }

      const types = {
        TransferWithAuthorization: [
          { name: 'from', type: 'address' },
          { name: 'to', type: 'address' },
          { name: 'value', type: 'uint256' },
          { name: 'validAfter', type: 'uint256' },
          { name: 'validBefore', type: 'uint256' },
          { name: 'nonce', type: 'bytes32' },
        ],
      }

      const values = {
        from: depositAccount.address,
        to: sweepToAddress,
        value: balance,
        validAfter,
        validBefore,
        nonce: authNonce,
      }

      // Sign the transferWithAuthorization message
      const signature = await depositAccount.signTypedData({
        domain,
        types,
        primaryType: 'TransferWithAuthorization',
        message: values,
      })

      // Parse signature
      const r = signature.slice(0, 66) as `0x${string}`
      const s = `0x${signature.slice(66, 130)}` as `0x${string}`
      const v = parseInt(signature.slice(130, 132), 16)

      // Execute transferWithAuthorization
      const transferHash = await client.walletClient.writeContract({
        address: USDC_CONTRACTS[chainName],
        abi: erc20WithPermitAbi,
        functionName: 'transferWithAuthorization',
        args: [depositAccount.address, sweepToAddress, balance, validAfter, validBefore, authNonce, v, r, s],
        account: client.walletClient.account!,
        chain: client.publicClient.chain,
      })

      // Wait for confirmation
      await client.publicClient.waitForTransactionReceipt({ hash: transferHash })

      this.logger.log(`Successfully swept USDC from ${chainName}. TX: ${transferHash}`)

      return {
        success: true,
        txHash: transferHash,
      }
    } catch (error) {
      this.logger.error(`Error sweeping from ${chainName}:`, error)

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Sweep USDC from Solana using gasless fee payer pattern
   */
  async sweepFromSolana(
    depositAddress: string,
    depositPrivateKey: string,
    sweepToAddress: string,
    feePayerPrivateKey: string,
  ): Promise<{ success: boolean; txHash?: string; error?: string; amountSwept?: string }> {
    try {
      // Validate and process deposit address private key
      let privateKeyBytes: Buffer
      try {
        if (depositPrivateKey.length >= 87 && depositPrivateKey.length <= 88) {
          try {
            privateKeyBytes = Buffer.from(bs58.decode(depositPrivateKey))
          } catch {
            privateKeyBytes = Buffer.from(depositPrivateKey, 'hex')
          }
        } else {
          privateKeyBytes = Buffer.from(depositPrivateKey, 'hex')
        }

        if (privateKeyBytes.length !== 64) {
          throw new Error(`Invalid deposit private key length: ${privateKeyBytes.length} bytes, expected 64 bytes`)
        }
      } catch (keyError) {
        return {
          success: false,
          error: `Invalid deposit private key: ${keyError instanceof Error ? keyError.message : 'Unknown error'}`,
        }
      }

      const depositKeypair = Keypair.fromSecretKey(privateKeyBytes)

      // Set up fee payer keypair
      let feePayerBytes: Buffer
      try {
        if (feePayerPrivateKey.length >= 87 && feePayerPrivateKey.length <= 88) {
          try {
            feePayerBytes = Buffer.from(bs58.decode(feePayerPrivateKey))
          } catch {
            feePayerBytes = Buffer.from(feePayerPrivateKey, 'hex')
          }
        } else {
          feePayerBytes = Buffer.from(feePayerPrivateKey, 'hex')
        }

        if (feePayerBytes.length !== 64) {
          throw new Error(`Invalid fee payer private key length: ${feePayerBytes.length} bytes, expected 64 bytes`)
        }
      } catch (keyError) {
        return {
          success: false,
          error: `Invalid fee payer private key: ${keyError instanceof Error ? keyError.message : 'Unknown error'}`,
        }
      }

      const feePayerKeypair = Keypair.fromSecretKey(feePayerBytes)

      // Get token accounts
      const usdcMint = this.getSolanaUsdcMint()
      const depositTokenAccount = getAssociatedTokenAddressSync(usdcMint, depositKeypair.publicKey)
      const sweepTokenAccount = getAssociatedTokenAddressSync(usdcMint, new PublicKey(sweepToAddress))

      // Check balance
      const tokenAccountInfo = await this.solanaConnection.getTokenAccountBalance(depositTokenAccount)
      const balance = tokenAccountInfo.value.uiAmount || 0

      if (balance === 0) {
        return {
          success: false,
          error: 'No USDC balance to sweep',
        }
      }

      // Create transfer instruction
      const transferInstruction = createTransferInstruction(
        depositTokenAccount,
        sweepTokenAccount,
        depositKeypair.publicKey,
        BigInt(tokenAccountInfo.value.amount),
        [],
        TOKEN_PROGRAM_ID,
      )

      // Send transaction with retry logic
      const maxRetries = 3
      let signature: string | null = null

      for (let attempt = 1; attempt <= maxRetries; attempt += 1) {
        try {
          const { blockhash, lastValidBlockHeight } = await this.solanaConnection.getLatestBlockhash('confirmed')

          const transaction = new Transaction()
          transaction.feePayer = feePayerKeypair.publicKey
          transaction.recentBlockhash = blockhash
          transaction.add(transferInstruction)

          transaction.partialSign(depositKeypair, feePayerKeypair)

          signature = await this.solanaConnection.sendRawTransaction(transaction.serialize(), {
            skipPreflight: false,
            preflightCommitment: 'confirmed',
          })

          await Promise.race([
            this.solanaConnection.confirmTransaction(
              {
                signature,
                blockhash,
                lastValidBlockHeight,
              },
              'confirmed',
            ),
            new Promise<void>((_, reject) => {
              setTimeout(() => reject(new Error('Transaction confirmation timeout')), 30000)
            }),
          ])

          break
        } catch (error) {
          if (attempt === maxRetries) {
            throw new Error(
              `All ${maxRetries} attempts failed. Last error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            )
          }

          const delay = Math.min(1000 * 2 ** (attempt - 1), 5000)
          await new Promise<void>((resolve) => {
            setTimeout(resolve, delay)
          })
        }
      }

      if (!signature) {
        throw new Error('Failed to get transaction signature after all retries')
      }

      return {
        success: true,
        txHash: signature,
        amountSwept: balance.toString(),
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }
}

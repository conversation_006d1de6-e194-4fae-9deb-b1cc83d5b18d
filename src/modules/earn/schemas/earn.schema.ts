import { z } from 'zod'

export const earnOption = z.object({
  id: z.string(),
  bondingPeriod: z.number(),
  chainId: z.string(),
  tokenId: z.string(),
  minimumDeposit: z.string(),
  tvl: z.string(),
  name: z.string(),
  description: z.string(),
  deployDate: z.string(),
  tokenToReceiveId: z.string(),
  protocol: z.number(),
  apy: z.string(),
  category: z.string(),
})

export const earnCategory = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  options: z.array(earnOption),
})

export const earnProtocolRule = z.object({
  id: z.string(),
  img: z.string(),
  title: z.string(),
  text: z.string().optional(),
  link: z.string(),
})

export const earnReceiptToken = z.object({
  tokenId: z.string(),
  symbol: z.string(),
  chainId: z.string(),
  returnTokenId: z.string(),
})

export const earnProtocol = z.object({
  id: z.number(),
  rating: z.enum(['excellent', 'good', 'moderate', 'low', 'average', 'poor']).optional(),
  launchDate: z.string(),
  category: z.string(),
  chains: z.array(z.string()),
  slug: z.string(),
  description: z.string(),
  name: z.string(),
  descriptionFormat: z.string(),
  website: z.string(),
  rules: z.array(earnProtocolRule),
  logo: z.string(),
  tokens: z.array(earnReceiptToken),
  tvl: z.string(),
  twitter: z.string(),
})

export type EarnProtocol = z.infer<typeof earnProtocol>
export type EarnCategory = z.infer<typeof earnCategory>

import { Injectable } from '@nestjs/common'

import type { EarnCategory, EarnProtocol } from './schemas/earn.schema'
import { EarnStorage } from './storage'

@Injectable()
export class EarnService {
  constructor(private storage: EarnStorage) {}

  async getEarnData(): Promise<{ protocols: EarnProtocol[]; categories: EarnCategory[] }> {
    const protocols = await this.storage.getEarnProtocols()
    const categories = await this.storage.getEarnCategories()

    return { protocols, categories }
  }
}

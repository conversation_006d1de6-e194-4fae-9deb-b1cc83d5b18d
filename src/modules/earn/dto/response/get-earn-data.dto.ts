import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsBoolean, ValidateNested } from 'class-validator'

import { EarnCategoryDto } from '../common/category.dto'
import { EarnProtocolDto } from '../common/protocol.dto'

class EarnDataDto {
  @ApiProperty({
    description: 'Array of earn categories',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EarnCategoryDto)
  categories: EarnCategoryDto[]

  @ApiProperty({
    description: 'Array of earn protocols',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EarnProtocolDto)
  protocols: EarnProtocolDto[]
}

export class GetEarnDataResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({ type: EarnDataDto })
  @ValidateNested()
  @Type(() => EarnDataDto)
  data: EarnDataDto
}

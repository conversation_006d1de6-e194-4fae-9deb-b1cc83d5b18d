import { Controller, Get, Logger } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { GetEarnDataResponseDto } from './dto/response/get-earn-data.dto'
import { EarnService } from './earn.service'

@ApiTags('Earn')
@Controller({
  path: '/earn',
  version: '1',
})
export class EarnController {
  private logger = new Logger('EarnController')

  constructor(private readonly earnService: EarnService) {}

  @ApiOperation({ description: 'Get earn protocols data' })
  @ApiOkResponse({ type: GetEarnDataResponseDto })
  @Get('/')
  async getEarnData(): Promise<GetEarnDataResponseDto> {
    this.logger.log(`Get earn protocols`)
    const { protocols, categories } = await this.earnService.getEarnData()

    return { success: true, data: { protocols, categories } }
  }
}

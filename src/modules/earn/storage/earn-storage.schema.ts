import { z } from 'zod'

const earnStorageProtocolOption = z.object({
  id: z.string(),
  bondingPeriod: z.number(),
  chainId: z.string(),
  tokenId: z.string(),
  minimumDeposit: z.number(),
  tvl: z.number(),
  name: z.string(),
  description: z.string(),
  deployDate: z.string(),
  tokenToReceiveId: z.string(),
  protocol: z.number(),
  apy: z.number(),
  category: z.string(),
})

const earnStorageProtocolRule = z.object({
  img: z.string(),
  title: z.string(),
  text: z.string().optional(),
  link: z.string(),
})

const earnStorageTokens = z.object({
  tokenId: z.string(),
  symbol: z.string(),
  chainId: z.string(),
  returnTokenId: z.string(),
})

export const earnStorageCategory = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  items: z.array(earnStorageProtocolOption),
})

export const earnStorageProtocol = z.object({
  id: z.number(),
  rating: z.enum(['excellent', 'good', 'moderate', 'low', 'average', 'poor']).optional(),
  launchDate: z.string(),
  category: z.string(),
  chains: z.array(z.string()),
  slug: z.string(),
  description: z.string(),
  name: z.string(),
  descriptionFormat: z.string(),
  website: z.string(),
  rules: z.array(earnStorageProtocolRule),
  logo: z.string(),
  tokens: z.array(earnStorageTokens),
  TVL: z.number(),
  twitter: z.string(),
})

export type EarnStorageProtocol = z.infer<typeof earnStorageProtocol>
export type EarnStorageCategory = z.infer<typeof earnStorageCategory>

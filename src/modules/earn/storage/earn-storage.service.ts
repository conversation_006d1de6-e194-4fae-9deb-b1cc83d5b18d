import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { <PERSON>ron, CronExpression } from '@nestjs/schedule'
import { nanoid } from 'nanoid'

import { Storage } from '@/abstract/storage'
import { parseNumberFromString } from '@/utils/amount/parse-numeric-string'
import { getErrorMessage } from '@/utils/get-error-message'

import {
  earnStorageCategory,
  type EarnStorageCategory,
  earnStorageProtocol,
  type EarnStorageProtocol,
} from './earn-storage.schema'
import { FirebaseService } from '../../firebase'
import { earnCategory, type EarnCategory, earnProtocol, type EarnProtocol } from '../schemas/earn.schema'

@Injectable()
export class EarnStorage extends Storage implements OnModuleInit {
  private logger = new Logger('EarnStorage')

  protocols: EarnProtocol[] = []
  categories: EarnCategory[] = []

  constructor(private firebase: FirebaseService) {
    super()
  }

  async onModuleInit() {
    void this.getEarnData().catch((error) =>
      this.logger.error(`Error on init EarnStorage ${getErrorMessage(error)}`, error),
    )
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    waitForCompletion: true,
    name: 'updateEarnData',
  })
  async updateEarnData(): Promise<void> {
    await this.getEarnData()
  }

  async getEarnData(): Promise<void> {
    await Promise.all([this.fetchEarnProtocols(), this.fetchEarnCategories()])
    this.setIsInitialized()
  }

  async getEarnProtocols(): Promise<EarnProtocol[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    return this.protocols
  }

  async getEarnCategories(): Promise<EarnCategory[]> {
    if (!this.isInitialized) await this.waitForInitPromise

    return this.categories
  }

  private async fetchEarnProtocols(): Promise<void> {
    try {
      const protocolsFromStorage = await this.firebase.getDocument<Record<string, EarnStorageProtocol>>(
        'earnProtocols',
        'allProtocols',
      )

      if (!protocolsFromStorage) throw new Error('Unable to fetch earn protocols')

      const validProtocols = Object.values(protocolsFromStorage)
        .filter((protocol) => typeof protocol === 'object')
        .filter((protocol) => {
          const parseRes = earnStorageProtocol.safeParse(protocol)

          if (!parseRes.success) {
            this.logger.error(`Cannot parse earn protocol from storage ${JSON.stringify(parseRes.error.errors)}`)
          }

          return parseRes.success
        })

      const protocols: EarnProtocol[] = validProtocols
        .map((category) => ({
          id: category.id,
          rating: category.rating,
          launchDate: category.launchDate,
          category: category.category,
          chains: category.chains,
          slug: category.slug,
          description: category.description,
          name: category.name,
          descriptionFormat: category.descriptionFormat,
          website: category.website,
          rules: category.rules.map((rule) => ({ ...rule, id: nanoid() })),
          logo: category.logo,
          tokens: category.tokens,
          tvl: parseNumberFromString(category.TVL),
          twitter: category.twitter,
        }))
        .filter((protocol) => {
          const parseRes = earnProtocol.safeParse(protocol)

          if (!parseRes.success) {
            this.logger.error(`Cannot parse mapped earn protocol ${JSON.stringify(parseRes.error.errors)}`)
          }

          return parseRes.success
        })

      if (!protocols.length) {
        this.logger.error('Cannot get earn categories')

        return
      }

      this.protocols = protocols
      this.logger.log('Fetched earn protocols from firebase')
    } catch (e) {
      this.logger.error(`Cannot get earn categories: ${getErrorMessage(e)}`)
    }
  }

  private async fetchEarnCategories(): Promise<void> {
    try {
      const categoriesFromStorage = await this.firebase.getDocument<{ categories: EarnStorageCategory[] }>(
        'earnPools',
        'allPools',
      )

      if (!categoriesFromStorage) throw new Error('Unable to fetch earn categories')

      const categories: EarnCategory[] = categoriesFromStorage.categories
        .filter((protocol) => {
          const parseRes = earnStorageCategory.safeParse(protocol)

          if (!parseRes.success) {
            this.logger.error(`Cannot parse earn category from storage ${JSON.stringify(parseRes.error.errors)}`)
          }

          return parseRes.success
        })
        .map((category) => ({
          id: category.id,
          title: category.title,
          description: category.description,
          options: category.items.map((item) => ({
            id: item.id,
            bondingPeriod: item.bondingPeriod,
            chainId: item.chainId,
            tokenId: item.tokenId,
            minimumDeposit: parseNumberFromString(item.minimumDeposit),
            tvl: parseNumberFromString(item.tvl),
            name: item.name,
            description: item.description,
            deployDate: item.deployDate,
            tokenToReceiveId: item.tokenToReceiveId,
            protocol: item.protocol,
            apy: parseNumberFromString(item.apy),
            category: item.category,
          })),
        }))
        .filter((protocol) => {
          const parseRes = earnCategory.safeParse(protocol)

          if (!parseRes.success) {
            this.logger.error(`Cannot parse mapped earn category ${JSON.stringify(parseRes.error.errors)}`)
          }

          return parseRes.success
        })

      if (!categories.length) {
        throw new Error('No valid categories in storage')
      }

      this.categories = categories
      this.logger.log('Fetched earn categories from firebase')
    } catch (e) {
      this.logger.error(`Cannot get earn categories: ${getErrorMessage(e)}`)
    }
  }
}

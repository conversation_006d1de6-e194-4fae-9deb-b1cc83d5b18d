import { Module } from '@nestjs/common'

import { ChainsModule } from '@/modules/chains'
import { TokensModule } from '@/modules/tokens'

import { HistoryController } from './history.controller'
import { HistoryService } from './history.service'
import { HistoryIndexerService } from './services/history-indexer.service'
import { FirebaseModule } from '../firebase'

@Module({
  exports: [HistoryService],
  imports: [FirebaseModule, TokensModule, ChainsModule],
  providers: [HistoryService, HistoryIndexerService],
  controllers: [HistoryController],
})
export class HistoryModule {}

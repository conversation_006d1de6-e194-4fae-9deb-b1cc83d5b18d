import type { IndexerIntent } from '../schemas/indexer-intent.schema'

type IntentType = 'cross-chain-swap' | 'cross-chain-transfer' | 'local-transfer' | 'local-swap' | 'stake' | 'unknown'

// https://github.com/inclusive-finance/indexer?tab=readme-ov-file#intent-type-field
export const mapTxType = (tx: IndexerIntent): IntentType => {
  switch (tx.intent_type) {
    case 'Cross Chain Swap':
      return 'cross-chain-swap'
    case 'Cross Chain Transfer':
      return 'cross-chain-transfer'
    case 'Local Transfer':
      return 'local-transfer'
    case 'Local Swap':
      return 'local-swap'
    case 'Stake':
      return 'stake'
    default:
      return 'unknown'
  }
}

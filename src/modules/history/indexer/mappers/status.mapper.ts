import type { TxStatus } from '../../schemas/tx-info.schema'
import type { IndexerIntent } from '../schemas/indexer-intent.schema'

export const mapStatus = (txInfo: IndexerIntent): TxStatus | undefined => {
  let status: TxStatus | undefined = 'processing'

  if (txInfo.status === 'Done') {
    status = 'success'
  } else if (txInfo.status === 'Failed') {
    status = 'error'
  } else if (txInfo.initial_data.intent_version === 6) {
    status = 'error'
  } else if (txInfo.initial_data.intent_version === 5 && !txInfo.initial_data.ack_result) {
    status = 'error'
  }

  return status
}

import { Injectable } from '@nestjs/common'

import type { TxInfo } from './schemas/tx-info.schema'
import { HistoryIndexerService } from './services/history-indexer.service'

@Injectable()
export class HistoryService {
  constructor(private readonly historyIndexerService: HistoryIndexerService) {}

  async getTxInfoByIntentId(id: number): Promise<TxInfo | undefined> {
    return this.historyIndexerService.getIntentInfo(id)
  }

  async getHistoryBySignerAddress(address: string): Promise<TxInfo[]> {
    const intents = await this.historyIndexerService.getIntentHistory(address)
    const deposits = await this.historyIndexerService.getDepositHistory(address)
    const totalHistory = intents.concat(deposits)

    totalHistory.sort((a, b) => b.timestamp - a.timestamp)

    return totalHistory
  }
}

import { z } from 'zod'

import {
  DepositTxInfo,
  depositTxInfoSchema,
  SendTxInfo,
  sendTxInfoSchema,
  SwapTxInfo,
  swapTxInfoSchema,
  YieldDepositTxInfo,
  yieldDepositTxInfoSchema,
  YieldWithdrawTxInfo,
  yieldWithdrawTxInfoSchema,
} from '../schemas/tx-info.schema'

export type HasDestinationInfo = SwapTxInfo | SendTxInfo | YieldDepositTxInfo | YieldWithdrawTxInfo

type HasIntentId = SwapTxInfo | SendTxInfo | YieldDepositTxInfo | YieldWithdrawTxInfo

type HasTxHash = DepositTxInfo

type HasMessageId = DepositTxInfo

export const isSwapTxInfo = (txInfo: unknown): txInfo is SwapTxInfo => swapTxInfoSchema.safeParse(txInfo).success
export const isSendTxInfo = (txInfo: unknown): txInfo is SendTxInfo => sendTxInfoSchema.safeParse(txInfo).success
export const isDepositTxInfo = (txInfo: unknown): txInfo is DepositTxInfo =>
  depositTxInfoSchema.safeParse(txInfo).success
export const isYieldTxInfo = (txInfo: unknown): txInfo is YieldDepositTxInfo =>
  yieldDepositTxInfoSchema.safeParse(txInfo).success
export const isYieldWithdrawTxInfo = (txInfo: unknown): txInfo is YieldWithdrawTxInfo =>
  yieldWithdrawTxInfoSchema.safeParse(txInfo).success

export const hasIntentId = (txInfo: unknown): txInfo is HasIntentId =>
  z
    .discriminatedUnion('type', [
      swapTxInfoSchema,
      sendTxInfoSchema,
      yieldDepositTxInfoSchema,
      yieldWithdrawTxInfoSchema,
    ])
    .safeParse(txInfo).success
export const hasTxHash = (txInfo: unknown): txInfo is HasTxHash => isDepositTxInfo(txInfo)
export const hasMessageId = (txInfo: unknown): txInfo is HasMessageId => isDepositTxInfo(txInfo)
export const hasDestinationInfo = (txInfo: unknown): txInfo is HasDestinationInfo => hasIntentId(txInfo)

import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsBoolean, IsNumber, ValidateNested } from 'class-validator'

import { BaseTxInfoDto } from '../common/tx-info.dto'

export class GetHistoryByAddressResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({ description: 'Total amount of transactions', example: 1 })
  @IsNumber()
  total: number

  @ApiProperty({
    description: 'History',
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseTxInfoDto)
  data: BaseTxInfoDto[]
}

import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean } from 'class-validator'

import { BaseTxInfoDto } from '../common/tx-info.dto'

export class GetTxInfoByIntentIdResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({
    description: 'Tx info',
    required: true,
  })
  @Type(() => BaseTxInfoDto)
  data: BaseTxInfoDto
}

import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsNumber } from 'class-validator'

export class GetTxInfoByIntentIdDto {
  @ApiProperty({ name: 'id', description: 'Intent ID', type: Number })
  @IsNumber()
  @Transform(({ value }) => {
    const number = Number(value)

    if (!Number.isNaN(number)) return number

    return value
  })
  id: number
}

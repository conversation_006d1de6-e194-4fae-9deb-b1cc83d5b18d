import { <PERSON>, Get, HttpException, Logger, Param } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { GetHistoryByAddressDto } from './dto/request/get-history-by-address.dto'
import { GetTxInfoByIntentIdDto } from './dto/request/get-tx-by-intent.dto'
import { GetHistoryByAddressResponseDto } from './dto/response/get-history-by-address.dto'
import { GetTxInfoByIntentIdResponseDto } from './dto/response/get-tx-by-intent.dto'
import { HistoryService } from './history.service'

@ApiTags('History')
@Controller({
  path: '/history',
  version: '1',
})
export class HistoryController {
  private logger = new Logger('HistoryController')

  constructor(private readonly historyService: HistoryService) {}

  @ApiOperation({ description: 'Get all history data' })
  @ApiOkResponse({ type: GetTxInfoByIntentIdResponseDto })
  @Get('/intent/:id')
  async getTxByIntentId(@Param() params: GetTxInfoByIntentIdDto): Promise<GetTxInfoByIntentIdResponseDto> {
    const { id } = params
    this.logger.log(`Request tx info by intent id ${id}`)
    const txInfo = await this.historyService.getTxInfoByIntentId(id)

    if (!txInfo) {
      throw new HttpException({ success: false }, 404)
    }

    return { success: true, data: txInfo }
  }

  @ApiOperation({ description: 'Get all history data' })
  @ApiOkResponse({ type: GetHistoryByAddressResponseDto })
  @Get('/address/:address')
  async getHistoryData(@Param() params: GetHistoryByAddressDto): Promise<GetHistoryByAddressResponseDto> {
    const { address } = params
    this.logger.log(`Request history for address ${address}`)
    const history = await this.historyService.getHistoryBySignerAddress(address)

    return { success: true, data: history, total: history.length }
  }
}

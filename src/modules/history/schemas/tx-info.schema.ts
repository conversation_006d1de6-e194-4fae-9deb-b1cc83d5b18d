import { z } from 'zod'

export const txStatusSchema = z.enum(['processing', 'success', 'error'])

export const baseTxInfoSchema = z.object({
  id: z.string(),
  timestamp: z.number(),
  feeUsd: z.string(),
  amountFrom: z.string(),
  amountFromUsd: z.string(),
  tokenIdFrom: z.string(),
  chainIdFrom: z.string(),
  status: txStatusSchema,
  explorerLink: z.string().optional(),
})

export const swapTxInfoSchema = baseTxInfoSchema.extend({
  intentId: z.number(),
  amountTo: z.string(),
  amountToUsd: z.string(),
  tokenIdTo: z.string(),
  chainIdTo: z.string(),
  type: z.enum(['buy', 'sell', 'convert'] as const),
})

export const depositTxInfoSchema = baseTxInfoSchema.extend({
  type: z.literal('deposit'),
  messageId: z.string(),
  txHash: z.string(),
})

export const sendTxInfoSchema = baseTxInfoSchema.extend({
  intentId: z.number(),
  amountTo: z.string(),
  amountToUsd: z.string(),
  tokenIdTo: z.string(),
  chainIdTo: z.string(),
  addressTo: z.string(),
  type: z.literal('send'),
})

export const yieldDepositTxInfoSchema = baseTxInfoSchema.extend({
  intentId: z.number(),
  amountTo: z.string(),
  amountToUsd: z.string(),
  tokenIdTo: z.string(),
  chainIdTo: z.string(),
  protocolId: z.number(),
  optionId: z.string(),
  type: z.literal('yield'),
})

export const yieldWithdrawTxInfoSchema = baseTxInfoSchema.extend({
  intentId: z.number(),
  amountTo: z.string(),
  amountToUsd: z.string(),
  tokenIdTo: z.string(),
  chainIdTo: z.string(),
  protocolId: z.number(),
  optionId: z.string(),
  type: z.literal('yield-withdraw'),
})

export const txInfoSchema = z.discriminatedUnion('type', [
  swapTxInfoSchema,
  depositTxInfoSchema,
  sendTxInfoSchema,
  yieldDepositTxInfoSchema,
  yieldWithdrawTxInfoSchema,
])

export type SwapTxInfo = z.infer<typeof swapTxInfoSchema>
export type DepositTxInfo = z.infer<typeof depositTxInfoSchema>
export type SendTxInfo = z.infer<typeof sendTxInfoSchema>
export type YieldDepositTxInfo = z.infer<typeof yieldDepositTxInfoSchema>
export type YieldWithdrawTxInfo = z.infer<typeof yieldWithdrawTxInfoSchema>

export type TxStatus = z.infer<typeof txStatusSchema>
export type TxInfo = z.infer<typeof txInfoSchema>

import { Body, Controller, Logger, Post, Headers, HttpException, HttpStatus } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'

import { getEnvConfig } from '@/config/env'

import { IntentEventDto } from './dto/common/intent-event.dto'
import { IntentChangedResponseDto } from './dto/response/intent-changed.dto'
import { NotificationsService } from './notifications.service'

@ApiTags('Notifications')
@Controller({
  path: '/notifications',
  version: '1',
})
export class NotificationsController {
  private logger = new Logger('NotificationsController')

  constructor(private readonly notificationsService: NotificationsService) {}

  @ApiOperation({ description: 'Send intent notification' })
  @ApiOkResponse({ type: IntentChangedResponseDto })
  @Post('/intent')
  async handleIntentEvent(
    @Body() intentEvent: IntentEventDto,
    @Headers('api-key') apiKey?: string,
  ): Promise<IntentChangedResponseDto> {
    if (!apiKey || apiKey !== getEnvConfig().notifications.notifyEventApiKey) {
      throw new HttpException({ success: false, message: 'API key is invalid' }, HttpStatus.FORBIDDEN)
    }

    if (intentEvent.type === 'intent_changed') {
      this.logger.log(`Handle intent event ${JSON.stringify(intentEvent)}`)
      const status = await this.notificationsService.sendIntentInfo(intentEvent.initiatorAddress, intentEvent)

      return { success: true, data: { status } }
    }

    this.logger.log(`Skip event type ${intentEvent.type}, data: ${JSON.stringify(intentEvent)}`)

    return { success: true, data: { status: 'skipped' } }
  }
}

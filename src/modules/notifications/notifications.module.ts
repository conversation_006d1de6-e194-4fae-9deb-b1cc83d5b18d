import { Module } from '@nestjs/common'

import { ChainsModule } from '@/modules/chains'
import { FirebaseModule } from '@/modules/firebase'
import { HistoryModule } from '@/modules/history'
import { TelegramModule } from '@/modules/telegram'
import { TokensModule } from '@/modules/tokens'

import { EmailChannelService, EmailTransportService } from './channels/email'
import { PushChannelService } from './channels/push'
import { NotificationsController } from './notifications.controller'
import { NotificationsService } from './notifications.service'
import { UserStorage } from './storage'

@Module({
  imports: [FirebaseModule, HistoryModule, TokensModule, ChainsModule, TelegramModule],
  providers: [NotificationsService, EmailChannelService, EmailTransportService, UserStorage, PushChannelService],
  controllers: [NotificationsController],
  exports: [EmailTransportService], // Export for use in other modules
})
export class NotificationsModule {}

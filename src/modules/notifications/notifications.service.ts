import { Injectable, Logger } from '@nestjs/common'

import { ChainsService } from '@/modules/chains'
import { hasIntentId, HistoryService } from '@/modules/history'
import { TelegramService } from '@/modules/telegram'
import { TokensService } from '@/modules/tokens'
import { formatFiatAmount } from '@/utils/amount/format-fiat-amount'

import { EmailChannelService } from './channels/email'
import { PushChannelService } from './channels/push'
import type { IntentEvent, IntentEventHandleStatus } from './schemas/event.schema'
import { UserStorage } from './storage'
import { SwapNotificationData } from './types/channel.types'

@Injectable()
export class NotificationsService {
  private logger = new Logger('NotificationsService')

  constructor(
    private emailChannel: EmailChannelService,
    private pushChannel: PushChannelService,
    private userStorage: UserStorage,
    private historyService: HistoryService,
    private tokensService: TokensService,
    private chainsService: ChainsService,
    private telegramService: TelegramService,
  ) {}

  async sendIntentInfo(signerAddress: string, data: IntentEvent): Promise<IntentEventHandleStatus> {
    if (![2, 5, 6].includes(data.version)) {
      this.logger.log(`Skip ${data.type} with version ${data.version}, data: ${JSON.stringify(data)}`)

      return 'skipped'
    }

    const userInfo = await this.userStorage.getUserInfo(signerAddress)

    if (!userInfo) {
      this.logger.warn(`Cannot find user info for address: ${signerAddress}`)

      return 'skipped'
    }

    const notificationData = await this.getNotificationData(data.intentId)

    if (!notificationData) return 'skipped'

    if (notificationData.txInfo.status !== 'processing') {
      this.sendLog(data.intentId, userInfo.email, notificationData)
    }

    if (['buy', 'sell', 'convert'].includes(notificationData.txInfo.type)) {
      const promises: Promise<void>[] = [this.emailChannel.sendSwapStatus(userInfo.email, notificationData)]

      if (userInfo.pushToken && ['success', 'error'].includes(notificationData.txInfo.status)) {
        promises.push(this.pushChannel.sendSwapStatus(userInfo.pushToken, notificationData))
      }

      await Promise.allSettled(promises)

      return 'handled'
    }

    return 'skipped'
  }

  private async getNotificationData(intentId: number): Promise<SwapNotificationData | undefined> {
    const txInfo = await this.historyService.getTxInfoByIntentId(intentId)

    if (!txInfo) return undefined

    if (!hasIntentId(txInfo)) {
      this.logger.warn(`Cannot find intent info for ${txInfo.id}`)

      return undefined
    }

    const tokenFrom = await this.tokensService.getTokenById(txInfo.tokenIdFrom)
    const tokenTo = await this.tokensService.getTokenById(txInfo.tokenIdTo)

    if (!tokenFrom || !tokenTo) {
      this.logger.warn(`Cannot find token info for ${tokenFrom ? txInfo.tokenIdFrom : txInfo.tokenIdTo}`)

      return undefined
    }

    const chainFrom = await this.chainsService.getChainById(txInfo.chainIdFrom)
    const chainTo = await this.chainsService.getChainById(txInfo.chainIdTo)

    if (!chainFrom || !chainTo) {
      this.logger.warn(`Cannot find chain info for ${chainFrom ? txInfo.chainIdFrom : txInfo.chainIdTo}`)

      return undefined
    }

    return { txInfo, tokenFrom, tokenTo, chainFrom, chainTo }
  }

  private sendLog(intentId: number, email: string, notificationData: SwapNotificationData): void {
    if (!hasIntentId(notificationData.txInfo)) return

    let statusEmoji
    let statusTitle

    if (notificationData.txInfo.status === 'success') {
      statusEmoji = '✅'
      statusTitle = 'Success'
    }

    if (notificationData.txInfo.status === 'error') {
      statusEmoji = '🚨'
      statusTitle = 'Failed'
    }

    const amountFromUsd = formatFiatAmount(notificationData.txInfo.amountFromUsd)
    const amountToUsd = formatFiatAmount(notificationData.txInfo.amountToUsd)

    this.telegramService.sendMessage(
      `
${statusTitle} ${statusEmoji} - Intent ${intentId}

Type: ${notificationData.txInfo.type}
From chain: ${notificationData.chainFrom.fullName}
To chain: ${notificationData.chainTo.fullName}
From amount: ${notificationData.txInfo.amountFrom} ${notificationData.tokenFrom.displayInfo.ticker} (${amountFromUsd}$)
To amount: ${notificationData.txInfo.amountTo} ${notificationData.tokenTo.displayInfo.ticker} (${amountToUsd}$)
Sender: ${email}

Link: ${notificationData.txInfo.explorerLink}
`.trim(),
      'HTML',
    )
  }
}

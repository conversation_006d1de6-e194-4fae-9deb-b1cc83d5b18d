import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean, ValidateNested } from 'class-validator'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

import { intentEventHandleStatusSchema } from '../../schemas/event.schema'

export class IntentChangedDataDto extends createZodDto(z.object({ status: intentEventHandleStatusSchema })) {}

export class IntentChangedResponseDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  success: boolean

  @ApiProperty({ type: IntentChangedDataDto, example: { status: 'handled' } })
  @ValidateNested()
  @Type(() => IntentChangedDataDto)
  data: IntentChangedDataDto
}

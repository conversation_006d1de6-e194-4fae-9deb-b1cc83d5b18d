import { Injectable, Logger } from '@nestjs/common'

import { FirebaseService } from '@/modules/firebase'

import { type UserInfo, userInfoSchema } from './user-storage.schema'

@Injectable()
export class UserStorage {
  private logger = new Logger('UserStorage')

  constructor(private firebase: FirebaseService) {}

  async getUserInfo(signerAddress: string): Promise<UserInfo | undefined> {
    const usersWithAddress = await this.firebase.getDocumentsWithCondition<UserInfo>('users', {
      fieldPath: 'userAddress',
      opStr: '==',
      value: signerAddress,
    })
    const userInfo = usersWithAddress?.[0]

    if (!userInfo) {
      this.logger.log(`Cannot find user info for address: ${signerAddress}`)

      return undefined
    }

    const parseRes = userInfoSchema.safeParse(userInfo)

    if (!parseRes.success) {
      this.logger.log(
        `Cannot parse user info for address: ${signerAddress}, errors: ${JSON.stringify(parseRes.error.errors)}`,
      )

      return undefined
    }

    this.logger.log(`Got user info for address: ${signerAddress}`)

    return userInfo
  }
}

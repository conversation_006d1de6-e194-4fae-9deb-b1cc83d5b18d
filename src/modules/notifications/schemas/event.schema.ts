import { z } from 'zod'

export const intentEventSchema = z.object({
  type: z.literal('intent_changed'),
  intentId: z.number(),
  version: z.number(),
  initiatorAddress: z.string(),
})

export const intentEventHandleStatusSchema = z.enum(['handled', 'skipped'] as const)

export type IntentEvent = z.infer<typeof intentEventSchema>
export type IntentEventHandleStatus = z.infer<typeof intentEventHandleStatusSchema>

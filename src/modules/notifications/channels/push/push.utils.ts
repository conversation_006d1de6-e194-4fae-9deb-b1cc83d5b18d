import { SwapNotificationData } from '../../types/channel.types'

export const formatBody = (notificationData: SwapNotificationData): string | undefined => {
  const { txInfo, tokenFrom, tokenTo } = notificationData
  const ticker = txInfo.type === 'buy' ? tokenTo.displayInfo.ticker : tokenFrom.displayInfo.ticker
  const amount = txInfo.type === 'buy' ? txInfo.amountTo : txInfo.amountFrom

  let message: string = ''

  switch (txInfo.type) {
    case 'buy':
      message = txInfo.status === 'error' ? 'Buying' : 'Bought'
      break
    case 'sell':
      message = txInfo.status === 'error' ? 'Selling' : 'Sold'
      break
    case 'convert':
      message = txInfo.status === 'error' ? 'Converting' : 'Converted'
      break
    case 'send':
      message = txInfo.status === 'error' ? 'Sending' : 'Sent'
      break
    case 'deposit':
      message = txInfo.status === 'error' ? 'Deposit' : 'Deposited'
      break
    case 'yield':
      message = txInfo.status === 'error' ? 'Yield deposit' : 'Yield deposit'
      break
    case 'yield-withdraw':
      message = txInfo.status === 'error' ? 'Yield withdrawal' : 'Yield withdrawal'
      break
    default:
      throw new Error(`Unknown tx type: ${JSON.stringify(txInfo)}`)
  }

  return `${message} ${amount} ${ticker}`
}

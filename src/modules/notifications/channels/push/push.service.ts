import { Injectable, Logger } from '@nestjs/common'
import { messaging } from 'firebase-admin'

import { FirebaseService } from '@/modules/firebase'
import { hasDestinationInfo } from '@/modules/history'
import { getErrorMessage } from '@/utils/get-error-message'

import type { PushTemplate } from './push.types'
import { formatBody } from './push.utils'
import { SwapNotificationData } from '../../types/channel.types'

@Injectable()
export class PushChannelService {
  private logger = new Logger('PushChannelService')

  constructor(private firebase: FirebaseService) {}

  async sendSwapStatus(pushToken: string, notificationData: SwapNotificationData): Promise<void> {
    try {
      const pushData = this.formatPush(pushToken, notificationData)
      const pushId = await messaging(this.firebase.adminApp).send(pushData)
      this.logger.log(`Sent push(${pushId}) on token ${pushToken}, intentId=${pushData.data.intentId}`)
    } catch (error) {
      this.logger.error(`Push failed: ${getErrorMessage(error)} with variables ${JSON.stringify(notificationData)}`)
      throw error
    }
  }

  private formatPush(pushToken: string, notificationData: SwapNotificationData): PushTemplate {
    const { txInfo } = notificationData

    if (!hasDestinationInfo(txInfo)) throw new Error('TxInfo does not contain destination info')

    return {
      token: pushToken,
      notification: {
        title: txInfo.status === 'success' ? `Transaction completed` : 'Transaction failed',
        body: formatBody(notificationData),
      },
      data: {
        intentId: String(txInfo.intentId),
        type: txInfo.status === 'success' ? 'success' : 'error',
        isHiddenInApp: 'true',
      },
    }
  }
}

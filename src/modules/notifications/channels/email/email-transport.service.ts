import { Injectable, Logger } from '@nestjs/common'
import axios, { AxiosInstance } from 'axios'

import { getEnvConfig } from '@/config/env'

export interface EmailOptions {
  to: string
  subject: string
  html: string
  from?: string // Optional, will use the provided sender if not specified
}

export interface EmailSenderConfig {
  senderName: string
  senderEmail: string
}

@Injectable()
export class EmailTransportService {
  private readonly logger = new Logger(EmailTransportService.name)
  private readonly mailCoachClient: AxiosInstance

  constructor() {
    const { mailCoach } = getEnvConfig()

    this.mailCoachClient = axios.create({
      baseURL: mailCoach.apiUrl,
      headers: {
        Authorization: `Bearer ${mailCoach.apiKey}`,
      },
    })

    this.logger.log('EmailTransportService configured with MailCoach client')
  }

  /**
   * Send an email using MailCoach with configurable sender
   */
  async sendEmail(options: EmailOptions, senderConfig: EmailSenderConfig): Promise<any> {
    try {
      const defaultSender = `${senderConfig.senderName} <${senderConfig.senderEmail}>`

      this.logger.log(`Sending email to: ${options.to}, subject: ${options.subject}, from: ${defaultSender}`)

      const result = await this.mailCoachClient.post('/transactional-mails/send', {
        subject: options.subject,
        html: options.html,
        from: options.from || defaultSender,
        to: options.to,
      })

      this.logger.log('Email sent successfully via MailCoach')

      return result.data
    } catch (error) {
      this.logger.error('Failed to send email via MailCoach:', error)
      throw error
    }
  }

  /**
   * Send multiple emails (batch sending) with configurable sender
   */
  async sendBulkEmails(emails: EmailOptions[], senderConfig: EmailSenderConfig): Promise<any[]> {
    const results = []

    for (const email of emails) {
      try {
        const result = await this.sendEmail(email, senderConfig)
        results.push({ success: true, email: email.to, result })
      } catch (error) {
        this.logger.error(`Failed to send email to ${email.to}:`, error)
        results.push({ success: false, email: email.to, error })
      }
    }

    return results
  }
}

import { Injectable, Logger } from '@nestjs/common'
import dayjs from 'dayjs'

import { getEnvConfig } from '@/config/env'
import { hasDestinationInfo, type TxInfo, type TxStatus } from '@/modules/history'
import { formatFiatAmount } from '@/utils/amount/format-fiat-amount'
import { getErrorMessage } from '@/utils/get-error-message'
import { getExplorerLinkForIntent } from '@/utils/get-explorer-link'

import { EmailTransportService, type EmailSenderConfig } from './email-transport.service'
import type { SwapTemplate } from './email.types'
import { formatNotificationName, formatStatus, formatTemplate } from './email.utils'
import { SwapNotificationData } from '../../types/channel.types'

@Injectable()
export class EmailChannelService {
  private logger = new Logger('EmailChannelService')
  private readonly senderConfig: EmailSenderConfig

  constructor(private emailTransport: EmailTransportService) {
    const { mailCoach } = getEnvConfig()
    this.senderConfig = {
      senderName: mailCoach.senderName,
      senderEmail: mailCoach.senderEmail,
    }
  }

  async sendSwapStatus(email: string, notificationData: SwapNotificationData): Promise<void> {
    try {
      const subject = this.getEmailSubject(notificationData.txInfo.type, notificationData.txInfo.status)
      const templateData = this.formatSwapDataForTemplate(notificationData)
      const htmlBody = formatTemplate(templateData)

      await this.emailTransport.sendEmail(
        {
          to: email,
          subject,
          html: htmlBody,
        },
        this.senderConfig,
      )

      this.logger.log(`Send email to ${email} for intent ${templateData.intentId}`)
    } catch (e) {
      const errorMessage = getErrorMessage(e)
      this.logger.error(errorMessage.concat(` with variables ${JSON.stringify(notificationData)}`))

      throw new Error(errorMessage)
    }
  }

  private formatSwapDataForTemplate(notificationData: SwapNotificationData): SwapTemplate {
    const { txInfo, tokenFrom, tokenTo, chainFrom, chainTo } = notificationData

    if (!hasDestinationInfo(txInfo)) throw new Error('TxInfo does not contain destination info')

    return {
      notificationName: formatNotificationName(txInfo.status),
      intentId: txInfo.intentId,
      transactionStatus: formatStatus(txInfo.status),
      transactionDate: dayjs(txInfo.timestamp).format('DD MMM YYYY, HH:mm'),
      transactionFromToken: `${txInfo.amountFrom} ${tokenFrom.displayInfo.ticker}`,
      transactionToToken: `${txInfo.amountTo} ${tokenTo.displayInfo.ticker}`,
      transactionFromChain: chainFrom.fullName,
      transactionToChain: chainTo.fullName,
      transactionFeeAmount: `$${formatFiatAmount(txInfo.feeUsd)}`,
      transactionLink: getExplorerLinkForIntent(txInfo.intentId),
    }
  }

  private getEmailSubject(type: TxInfo['type'], status: TxStatus): string {
    switch (type) {
      case 'buy':
      case 'sell':
      case 'convert':
        switch (status) {
          case 'success':
            return 'Transaction completed'
          case 'processing':
            return 'Transaction submitted'
          case 'error':
            return 'Transaction failed'
          default:
            throw new Error(`Unknown swap status: ${status}`)
        }
      default:
        throw new Error(`Unknown swap type: ${type}`)
    }
  }
}

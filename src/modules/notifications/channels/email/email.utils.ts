import dayjs from 'dayjs'

import { type TxStatus } from '@/modules/history'

import type { SwapTemplate } from './email.types'

export const formatStatus = (status: TxStatus): string => {
  switch (status) {
    case 'processing':
      return 'In progress'
    case 'success':
      return 'Success'
    case 'error':
      return 'Failed'
    default:
      throw new Error(`Unknown status: ${status}`)
  }
}

export const formatNotificationName = (status: TxStatus): string => {
  switch (status) {
    case 'processing':
      return 'submitted'
    case 'success':
      return 'completed'
    case 'error':
      return 'failed'
    default:
      throw new Error(`Unknown status: ${status}`)
  }
}

export const formatTemplate = (templateData: SwapTemplate): string => `
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Your transaction ${templateData.notificationName}</title>
  </head>
  <body style="margin: 0; padding: 0; background-color: #ffffff; color: #111827; font-family: Arial, sans-serif;">
    <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #ffffff; padding: 24px 0;">
      <tr>
        <td align="center">
          <table width="600" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px;">
            <tr>
              <td align="center" style="padding-bottom: 24px;">
                <img src="https://d3b9kr64nievew.cloudfront.net/cm9a4hpvh00r5sbk5iw1ae3mi/cm9a4hpz200r7sbk5oxl39w65.png" alt="Caishen Logo" width="80" style="display: block;" />
              </td>
            </tr>
            <tr>
              <td align="center">
                <h1 style="font-size: 24px; font-weight: bold; margin: 16px 0;">Your transaction ${templateData.notificationName}</h1>
              </td>
            </tr>
            <tr>
              <td>
                <h2 style="font-size: 18px; font-weight: bold; text-align: center;">Transaction details</h2>
              </td>
            </tr>

            <!-- transaction info table -->
            <tr>
              <td>
                <table width="100%" cellpadding="12" cellspacing="0" style="border-collapse: collapse; margin-top: 8px;">
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">Intent ID:</td>
                    <td align="right">${templateData.intentId}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">Status:</td>
                    <td align="right">${templateData.transactionStatus}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">Date:</td>
                    <td align="right">${templateData.transactionDate}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">From:</td>
                    <td align="right">${templateData.transactionFromToken}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">To:</td>
                    <td align="right">${templateData.transactionToToken}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">From chain:</td>
                    <td align="right">${templateData.transactionFromChain}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">To chain:</td>
                    <td align="right">${templateData.transactionToChain}</td>
                  </tr>
                  <tr style="border-top: 1px solid #e5e7eb; border-bottom: 1px solid #e5e7eb;">
                    <td style="font-weight: bold;">Fee:</td>
                    <td align="right">${templateData.transactionFeeAmount}</td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- button -->
            <tr>
              <td align="center" style="padding-top: 32px;">
                <a href="${templateData.transactionLink}" style="display: inline-block; background-color: #6C1CEE; color: #ffffff; text-decoration: none; padding: 16px 40px; border-radius: 25px; font-weight: 600">
                  View Transaction
                </a>
              </td>
            </tr>

            <!-- footer -->
            <tr>
              <td align="center" style="font-size: 14px; color: #9ca3af; padding-top: 40px;">
                <p style="margin: 4px;"><a href="https://caishen.io" style="color: #9ca3af; text-decoration: none;">Website</a> • <a href="https://x.com/caishen_app" style="color: #9ca3af; text-decoration: none;">X (Twitter)</a></p>
                <p style="margin: 4px;">Copyright © ${dayjs().get('year')} Caishen</p>
              </td>
            </tr>

          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
`

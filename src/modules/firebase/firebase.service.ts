import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import * as admin from 'firebase-admin'
import * as firestore from 'firebase-admin/firestore'

import { getErrorMessage } from '@/utils/get-error-message'

import type { FirebaseCondition } from './firebase.types'
import { getEnvConfig } from '../../config/env'

@Injectable()
export class FirebaseService implements OnModuleInit {
  private logger = new Logger('FirebaseService')

  adminApp: admin.app.App
  private db: firestore.Firestore

  async onModuleInit() {
    try {
      const { firebase } = getEnvConfig()
      this.adminApp = admin.initializeApp({ credential: admin.credential.cert(firebase) })
      this.db = admin.firestore(this.adminApp)
      this.logger.log('UserStorage initialized')
    } catch (error) {
      this.logger.error(`Error on init UserStorage ${getErrorMessage(error)}`, error)

      throw error
    }
  }

  async getDocument<T>(collection: string, id: string): Promise<T | undefined> {
    const doc = await this.db.collection(collection).doc(id).get()

    if (!doc.exists) return undefined

    return doc.data() as T
  }

  async getAllDocuments<T>(collection: string): Promise<T[] | undefined> {
    const doc = await this.db.collection(collection).get()

    if (doc.empty) return undefined

    return doc.docs.map((d) => d.data()) as T[]
  }

  async getDocumentsWithCondition<T>(collection: string, condition: FirebaseCondition): Promise<T[] | undefined> {
    const doc = await this.db.collection(collection).where(condition.fieldPath, condition.opStr, condition.value).get()

    if (doc.empty) return undefined

    return doc.docs.map((d) => d.data()) as T[]
  }

  async saveDocument<T extends object>(collection: string, data: T, id?: string, withMerge?: boolean): Promise<string> {
    const collectionRef = this.db.collection(collection)
    let docId = id

    if (!docId) {
      docId = collectionRef.doc().id
    }

    await collectionRef.doc(docId).set(data, { merge: withMerge })

    return docId
  }

  async saveDocuments<T extends object>(
    collection: string,
    docs: { data: T; id: string }[],
    withMerge?: boolean,
  ): Promise<void> {
    const batch = this.db.batch()

    docs.forEach(({ data, id }) => {
      const ref = this.db.collection(collection).doc(id)
      batch.set(ref, data, { merge: withMerge })
    })

    await batch.commit()
  }

  async deleteDocument(collection: string, id: string): Promise<void> {
    const collectionRef = this.db.collection(collection)
    await collectionRef.doc(id).delete()
  }

  async deleteDocuments(collection: string, ids: string[]): Promise<void> {
    const batch = this.db.batch()

    ids.forEach((id) => {
      const ref = this.db.collection(collection).doc(id)
      batch.delete(ref)
    })

    await batch.commit()
  }

  async deleteAllDocumentsInCollection(collection: string): Promise<void> {
    const collectionRef = this.db.collection(collection)
    const snapshot = await collectionRef.limit(500).get()

    if (snapshot.empty) return

    const batch = this.db.batch()
    snapshot.docs.forEach((doc) => batch.delete(doc.ref))
    await batch.commit()
    await this.deleteAllDocumentsInCollection(collection)
  }

  createDocId(collection: string): string {
    const collectionRef = this.db.collection(collection)

    return collectionRef.doc().id
  }
}

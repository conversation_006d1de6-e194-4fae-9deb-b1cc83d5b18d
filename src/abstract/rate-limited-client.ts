import axios, { type AxiosInstance, type CreateAxiosDefaults } from 'axios'
import axiosRetry, { type IAxiosRetryConfig } from 'axios-retry'

import { sleep } from '@/utils/promise/sleep'

export abstract class RateLimitedClient {
  protected client: AxiosInstance
  protected lastRequestTimestamp: number
  protected durationBetweenRequests: number // in ms

  constructor(args: {
    clientConfig: CreateAxiosDefaults
    retryConfig?: IAxiosRetryConfig
    durationBetweenRequests?: number
  }) {
    this.client = axios.create(args.clientConfig)
    this.durationBetweenRequests = args.durationBetweenRequests || 5000

    this.client.interceptors.response.use(
      (response) => {
        this.lastRequestTimestamp = Date.now()

        return response
      },
      (error) => {
        this.lastRequestTimestamp = Date.now()

        return Promise.reject(error)
      },
    )

    axiosRetry(this.client, {
      retries: 3,
      retryDelay: (retryCount) => 5000 * retryCount,
      ...args.retryConfig,
    })
  }

  protected async checkRateLimit(): Promise<void> {
    const now = Date.now()

    if (this.lastRequestTimestamp && now - this.lastRequestTimestamp < this.durationBetweenRequests) {
      const diff = now - this.lastRequestTimestamp
      const waitTime = this.durationBetweenRequests - diff

      await sleep(waitTime)
    }
  }
}

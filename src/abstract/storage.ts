export abstract class Storage {
  protected isInitialized = false
  protected isInitializing = true
  protected waitForInitPromise: Promise<void>
  protected setIsInitialized: () => void

  protected constructor() {
    this.waitForInitPromise = new Promise((resolve) => {
      this.setIsInitialized = () => {
        this.isInitialized = true
        this.isInitializing = false
        resolve()
      }
    })
  }

  revalidateCache(): void {
    this.isInitialized = false
    this.isInitializing = true

    this.waitForInitPromise = new Promise((resolve) => {
      this.setIsInitialized = () => {
        this.isInitialized = true
        this.isInitializing = false
        resolve()
      }
    })
  }
}

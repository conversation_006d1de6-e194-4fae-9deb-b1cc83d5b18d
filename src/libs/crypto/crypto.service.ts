import { createCipher<PERSON>, createDecipheriv, randomBytes, scrypt } from 'crypto'
import { promisify } from 'util'

import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '@/config/env'

const scryptAsync = promisify(scrypt)

@Injectable()
export class CryptoService {
  private readonly logger = new Logger(CryptoService.name)
  private readonly envConfig = getEnvConfig()

  constructor() {
    if (!this.envConfig.crypto.secretKey) {
      throw new Error('CRYPTO_SECRET_KEY environment variable is required')
    }

    this.logger.log('Initialized crypto service with AES-256-GCM encryption')
  }

  /**
   * Encrypt a private key using AES-256-GCM
   */
  async encryptPrivateKey(privateKey: string): Promise<string> {
    try {
      // Generate a random salt and IV
      const salt = randomBytes(16)
      const iv = randomBytes(16)

      // Derive key from secret using scrypt
      const key = (await scryptAsync(this.envConfig.crypto.secretKey, salt, 32)) as Buffer

      // Create cipher
      const cipher = createCipheriv('aes-256-gcm', key, iv)

      // Encrypt the private key
      let encrypted = cipher.update(privateKey, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      // Get the authentication tag
      const authTag = cipher.getAuthTag()

      // Combine salt, iv, authTag, and encrypted data
      const result = {
        salt: salt.toString('hex'),
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        encrypted,
      }

      // Return as base64-encoded JSON for easy storage
      const combined = Buffer.from(JSON.stringify(result), 'utf8').toString('base64')

      this.logger.log('Successfully encrypted private key with AES-256-GCM')

      return combined
    } catch (error) {
      this.logger.error('Failed to encrypt private key:', error)
      throw new Error(`Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Decrypt a private key using AES-256-GCM
   */
  async decryptPrivateKey(encryptedPrivateKey: string): Promise<string> {
    try {
      // Parse the encrypted data
      const combined = Buffer.from(encryptedPrivateKey, 'base64').toString('utf8')
      const data = JSON.parse(combined)

      const salt = Buffer.from(data.salt, 'hex')
      const iv = Buffer.from(data.iv, 'hex')
      const authTag = Buffer.from(data.authTag, 'hex')
      const { encrypted } = data

      // Derive the same key using scrypt
      const key = (await scryptAsync(this.envConfig.crypto.secretKey, salt, 32)) as Buffer

      // Create decipher
      const decipher = createDecipheriv('aes-256-gcm', key, iv)
      decipher.setAuthTag(authTag)

      // Decrypt the private key
      let decrypted = decipher.update(encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      this.logger.log('Successfully decrypted private key with AES-256-GCM')

      return decrypted
    } catch (error) {
      this.logger.error('Failed to decrypt private key:', error)
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate that the crypto service is properly configured
   */
  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!this.envConfig.crypto.secretKey) {
      errors.push('CRYPTO_SECRET_KEY environment variable not set')
    } else if (this.envConfig.crypto.secretKey.length < 32) {
      errors.push('CRYPTO_SECRET_KEY should be at least 32 characters long for security')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * Generate a secure random secret key for initial setup
   */
  static generateSecretKey(): string {
    return randomBytes(32).toString('hex')
  }
}

import { Module } from '@nestjs/common'

import { ChainsModule } from '@/modules/chains'

import { PoolIndexerService } from './services/pool-indexer.service'
import { PriceIndexerService } from './services/price-indexer.service'

@Module({
  imports: [ChainsModule],
  providers: [PoolIndexerService, PriceIndexerService],
  exports: [PoolIndexerService, PriceIndexerService],
})
export class IndexerModule {}

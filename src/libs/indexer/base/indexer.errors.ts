export class IndexerError extends Error {
  constructor(
    message: string,
    public readonly statusCode?: number,
    public readonly endpoint?: string,
  ) {
    super(message)
    this.name = 'IndexerError'
  }
}

export class IndexerApiError extends IndexerError {
  constructor(message: string, statusCode?: number, endpoint?: string) {
    super(message, statusCode, endpoint)
    this.name = 'IndexerApiError'
  }
}

export class IndexerValidationError extends IndexerError {
  constructor(message: string, endpoint?: string) {
    super(message, undefined, endpoint)
    this.name = 'IndexerValidationError'
  }
}

export class IndexerNetworkError extends IndexerError {
  constructor(message: string, endpoint?: string) {
    super(message, undefined, endpoint)
    this.name = 'IndexerNetworkError'
  }
}

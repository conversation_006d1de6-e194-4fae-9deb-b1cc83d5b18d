import { z } from 'zod'

// Common indexer configuration
export interface IndexerConfig {
  baseURL: string
  timeout?: number
}

// Pool-related types
export interface TokenDetails {
  tokenAddress: string
  decimals: string
  name: string | null
}

export interface PoolLiquidityOptions {
  pmLiquidityOnly?: boolean
}

// Price-related types
export interface TokenPriceRequest {
  token_id: string
  chain_id: number[]
  pool_address: string[]
}

export interface TokenPriceResponse {
  token_id: string
  chain_id: string
  price_usd: number
  timestamp: number
  source?: string
}

// Common response schemas
export const totalPoolLiquidityResponseSchema = z.string()
export const totalPmLiquidityResponseSchema = z.string()
export const tokenDetailsResponseSchema = z.object({
  token_address: z.string(),
  decimals: z.string(),
  name: z.string().nullable(),
})

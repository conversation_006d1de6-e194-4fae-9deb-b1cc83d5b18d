import { Logger } from '@nestjs/common'
import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from 'axios'
import { z } from 'zod'

import { getErrorMessage } from '@/utils/get-error-message'

import { IndexerApiError, IndexerNetworkError, IndexerValidationError } from './indexer.errors'
import { IndexerConfig } from './indexer.types'

export abstract class IndexerBaseClient {
  protected readonly client: AxiosInstance
  protected abstract readonly logger: Logger

  constructor(config: IndexerConfig) {
    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`Making request to ${config.method?.toUpperCase()} ${config.url}`)

        return config
      },
      (error) => {
        this.logger.error(`Request error: ${getErrorMessage(error)}`)

        return Promise.reject(error)
      },
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`Response received from ${response.config.url} with status ${response.status}`)

        return response
      },
      (error) => {
        this.logger.error(`Response error: ${getErrorMessage(error)}`)

        return Promise.reject(error)
      },
    )
  }

  /**
   * Handle and classify errors from API calls
   */
  protected handleApiError(error: unknown, endpoint: string): Error {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError

      // Network or connection errors
      if (!axiosError.response) {
        return new IndexerNetworkError(`Network error when calling ${endpoint}: ${axiosError.message}`, endpoint)
      }

      // HTTP error responses
      const statusCode = axiosError.response.status
      const { statusText } = axiosError.response

      if (statusCode >= 500) {
        return new IndexerApiError(
          `Server error (${statusCode}) when calling ${endpoint}: ${statusText}`,
          statusCode,
          endpoint,
        )
      }

      if (statusCode === 404) {
        return new IndexerApiError(`Resource not found when calling ${endpoint}`, statusCode, endpoint)
      }

      if (statusCode >= 400) {
        return new IndexerApiError(
          `Client error (${statusCode}) when calling ${endpoint}: ${statusText}`,
          statusCode,
          endpoint,
        )
      }
    }

    // Validation errors (from zod)
    if (error instanceof z.ZodError) {
      return new IndexerValidationError(`Invalid response format from ${endpoint}: ${error.message}`, endpoint)
    }

    // Generic error fallback
    return new IndexerApiError(`Unknown error when calling ${endpoint}: ${getErrorMessage(error)}`, undefined, endpoint)
  }

  /**
   * Make a GET request with error handling
   */
  protected async get<T>(endpoint: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.get<T>(endpoint, config)

      return response.data
    } catch (error) {
      throw this.handleApiError(error, endpoint)
    }
  }

  /**
   * Make a POST request with error handling
   */
  protected async post<T>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.post<T>(endpoint, data, config)

      return response.data
    } catch (error) {
      throw this.handleApiError(error, endpoint)
    }
  }
}

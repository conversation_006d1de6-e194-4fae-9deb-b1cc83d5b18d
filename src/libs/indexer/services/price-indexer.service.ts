import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '@/config/env'
import { ChainsService } from '@/modules/chains'

import { IndexerBaseClient } from '../base/indexer-base.client'
import { TokenPriceRequest, TokenPriceResponse } from '../base/indexer.types'

@Injectable()
export class PriceIndexerService extends IndexerBaseClient {
  protected readonly logger = new Logger(PriceIndexerService.name)

  constructor(private readonly chainsService: ChainsService) {
    const config = getEnvConfig()
    super({
      baseURL: config.pricing.apiUrl,
      timeout: 10000,
    })
  }

  /**
   * Fetch token prices from the indexer for multiple tokens
   */
  async getTokenPrices(
    tokens: { tokenId: string; chainId: string; poolAddress?: string }[],
  ): Promise<TokenPriceResponse[]> {
    try {
      this.logger.log(`Fetching prices for ${tokens.length} tokens from indexer`)

      const allPrices: TokenPriceResponse[] = []

      // Make individual requests for each token since API expects single token format
      for (const token of tokens) {
        try {
          // Skip tokens without pool address since it's required by the API
          if (!token.poolAddress) {
            this.logger.warn(`Skipping token ${token.tokenId} - no pool address provided`)
            continue
          }

          const chainId = await this.getChainIdFromService(token.chainId)

          if (!chainId) {
            this.logger.warn(`Skipping token ${token.tokenId} - unsupported chain: ${token.chainId}`)
            continue
          }

          const request: TokenPriceRequest = {
            token_id: token.tokenId,
            chain_id: [chainId],
            pool_address: [token.poolAddress],
          }

          const response = await this.post<TokenPriceResponse>('/token_price', request)

          if (response) {
            allPrices.push(response)
          }
        } catch (error: any) {
          // Don't fail the entire batch if one token fails
          if (error?.response?.status === 404) {
            this.logger.warn(`Price not found for token ${token.tokenId} on chain ${token.chainId}`)
          } else {
            this.logger.error(`Failed to fetch price for token ${token.tokenId}:`, error?.message || error)
          }
        }
      }

      this.logger.log(`Successfully fetched ${allPrices.length} token prices from indexer`)

      return allPrices
    } catch (error) {
      this.logger.error('Failed to fetch token prices from indexer:', error)
      throw new Error(`Indexer price fetch failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Fetch price for a single token
   */
  async getTokenPrice(tokenId: string, chainId: string, poolAddress?: string): Promise<TokenPriceResponse | null> {
    try {
      const prices = await this.getTokenPrices([{ tokenId, chainId, poolAddress }])

      return prices.find((price) => price.token_id === tokenId) || null
    } catch (error) {
      this.logger.error(`Failed to fetch price for token ${tokenId}:`, error)

      return null
    }
  }

  /**
   * Get numeric chain ID from ChainsService
   */
  private async getChainIdFromService(chainName: string): Promise<number | null> {
    try {
      const chainInfo = await this.chainsService.getChainById(chainName)

      return chainInfo?.chainId || null
    } catch (error) {
      this.logger.error(`Failed to get chain ID for ${chainName}:`, error)

      return null
    }
  }

  /**
   * Check if the indexer is available
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Try a simple request with a dummy token to test connectivity
      const testRequest: TokenPriceRequest = {
        token_id: 'health-check-dummy',
        chain_id: [1], // Ethereum
        pool_address: ['******************************************'], // Dummy pool address for health check only
      }

      await this.post('/token_price', testRequest, {
        timeout: 5000, // Shorter timeout for health check
      })

      // If we get here without throwing, the API is responding
      this.logger.log('Indexer health check passed - API is responding')

      return true
    } catch (error: any) {
      // Check if it's a connection error vs a valid response
      if (error?.response?.status === 404) {
        // 404 with proper JSON response means the API is working but no data found
        // This is actually a successful health check
        this.logger.log('Indexer health check passed - API is responding (404 is expected for dummy token)')

        return true
      }

      if (error?.response?.status && error?.response?.status < 500) {
        // Other 4xx errors also indicate the API is responding
        this.logger.log(`Indexer health check passed - API is responding (status: ${error.response.status})`)

        return true
      }

      // Network errors, timeouts, or 5xx errors indicate the API is not available
      this.logger.warn('Indexer health check failed:', {
        message: error?.message,
        status: error?.response?.status,
        statusText: error?.response?.statusText,
      })

      return false
    }
  }
}

import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '@/config/env'
import { getErrorMessage } from '@/utils/get-error-message'

import { IndexerBaseClient } from '../base/indexer-base.client'
import { IndexerApiError, IndexerNetworkError } from '../base/indexer.errors'
import {
  TokenDetails,
  PoolLiquidityOptions,
  totalPoolLiquidityResponseSchema,
  totalPmLiquidityResponseSchema,
  tokenDetailsResponseSchema,
} from '../base/indexer.types'

@Injectable()
export class PoolIndexerService extends IndexerBaseClient {
  protected readonly logger = new Logger(PoolIndexerService.name)

  constructor() {
    const envConfig = getEnvConfig()
    super({
      baseURL: envConfig.indexer.apiUrl,
      timeout: 10000,
    })
  }

  /**
   * Get total pool liquidity for a specific pool address
   */
  async getTotalPoolLiquidity(poolAddress: string, options?: PoolLiquidityOptions): Promise<string> {
    const endpoint = `/total_pool_liquidity/${poolAddress}`

    try {
      this.logger.log(`Getting total pool liquidity for pool: ${poolAddress}`)

      const params = options?.pmLiquidityOnly ? { pm_liquidity_only: true } : {}
      const response = await this.get<string>(endpoint, { params })

      // Validate response
      const validatedData = totalPoolLiquidityResponseSchema.parse(response)

      this.logger.log(`Successfully retrieved pool liquidity: ${validatedData}`)

      return validatedData
    } catch (error) {
      const handledError = this.handleApiError(error, endpoint)
      this.logger.error(`Failed to get total pool liquidity for ${poolAddress}: ${handledError.message}`)
      throw handledError
    }
  }

  /**
   * Get total PM (Project Manager) liquidity across all pools
   */
  async getTotalPmLiquidity(): Promise<string> {
    const endpoint = '/total_pm_liquidity'

    try {
      this.logger.log('Getting total PM liquidity')

      const response = await this.get<string>(endpoint)

      // Validate response
      const validatedData = totalPmLiquidityResponseSchema.parse(response)

      this.logger.log(`Successfully retrieved total PM liquidity: ${validatedData}`)

      return validatedData
    } catch (error) {
      const handledError = this.handleApiError(error, endpoint)
      this.logger.error(`Failed to get total PM liquidity: ${handledError.message}`)
      throw handledError
    }
  }

  /**
   * Get token details by token address and chain ID
   */
  async getTokenDetails(tokenAddress: string, chainId: string): Promise<TokenDetails> {
    const endpoint = `/tokens/${tokenAddress}/${chainId}`

    try {
      this.logger.log(`Getting token details for ${tokenAddress} on chain ${chainId}`)

      const response = await this.get<any>(endpoint)

      // Validate response
      const validatedData = tokenDetailsResponseSchema.parse(response)

      const tokenDetails: TokenDetails = {
        tokenAddress: validatedData.token_address,
        decimals: validatedData.decimals,
        name: validatedData.name,
      }

      this.logger.log(`Successfully retrieved token details for ${tokenAddress}`)

      return tokenDetails
    } catch (error) {
      const handledError = this.handleApiError(error, endpoint)
      this.logger.error(`Failed to get token details for ${tokenAddress} on chain ${chainId}: ${handledError.message}`)
      throw handledError
    }
  }

  /**
   * Check if a pool has liquidity by checking if total liquidity is greater than zero
   */
  async hasPoolLiquidity(poolAddress: string): Promise<boolean> {
    try {
      const totalLiquidity = await this.getTotalPoolLiquidity(poolAddress)
      const liquidityAmount = BigInt(totalLiquidity)

      return liquidityAmount > 0n
    } catch (error) {
      // Log the specific error type for better debugging
      if (error instanceof IndexerApiError) {
        this.logger.warn(`API error checking pool liquidity for ${poolAddress} (${error.statusCode}): ${error.message}`)
      } else if (error instanceof IndexerNetworkError) {
        this.logger.warn(`Network error checking pool liquidity for ${poolAddress}: ${error.message}`)
      } else {
        this.logger.error(`Failed to check pool liquidity for ${poolAddress}: ${getErrorMessage(error)}`)
      }

      // Return false as a safe fallback - no liquidity assumed if we can't check
      return false
    }
  }

  /**
   * Check if a specific pool has PM (Project Manager) liquidity
   */
  async hasPoolPmLiquidity(poolAddress: string): Promise<boolean> {
    try {
      const pmLiquidity = await this.getTotalPoolLiquidity(poolAddress, { pmLiquidityOnly: true })
      const liquidityAmount = BigInt(pmLiquidity)

      return liquidityAmount > 0n
    } catch (error) {
      // Log the specific error type for better debugging
      if (error instanceof IndexerApiError) {
        this.logger.warn(`API error checking PM liquidity for ${poolAddress} (${error.statusCode}): ${error.message}`)
      } else if (error instanceof IndexerNetworkError) {
        this.logger.warn(`Network error checking PM liquidity for ${poolAddress}: ${error.message}`)
      } else {
        this.logger.error(`Failed to check PM liquidity for ${poolAddress}: ${getErrorMessage(error)}`)
      }

      // Return false as a safe fallback - no liquidity assumed if we can't check
      return false
    }
  }
}

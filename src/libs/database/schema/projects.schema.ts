import { relations } from 'drizzle-orm'
import {
  pgTable,
  text,
  uuid,
  varchar,
  timestamp,
  decimal,
  integer,
  date,
  serial,
  jsonb,
  pgEnum,
  boolean,
} from 'drizzle-orm/pg-core'

// Enums
export const submissionStatusEnum = pgEnum('submission_status', [
  'draft',
  'submitted',
  'under_review',
  'needs_revision',
  'approved',
  'wallet_provided',
  'fee_paid',
  'liquidity_provided',
])

export const blockchainEnum = pgEnum('blockchain_type', [
  'ethereum',
  'solana',
  'polygon',
  'bsc',
  'arbitrum',
  'base',
  'optimism',
])

export const tgeTypeEnum = pgEnum('tge_type', ['fixed_price', 'auction', 'bonding_curve'])

export const launchTypeEnum = pgEnum('launch_type', ['fair_launch', 'curated'])

// Main projects table
export const projects = pgTable('projects', {
  projectId: uuid('project_id').primaryKey().defaultRandom(),
  userId: text('user_id').notNull(), // Reference to auth users table

  // Page 1: Basic Information
  projectName: varchar('project_name', { length: 255 }).notNull(),
  projectWebsite: varchar('project_website', { length: 2048 }).notNull(),
  contactName: varchar('contact_name', { length: 255 }).notNull(),
  contactEmail: varchar('contact_email', { length: 255 }).notNull(),
  roleInProject: varchar('role_in_project', { length: 255 }).notNull(),
  blockchainToLaunchOn: blockchainEnum('blockchain_to_launch_on').notNull(),

  // Launch Type and Curation
  launchType: launchTypeEnum('launch_type').notNull().default('fair_launch'),
  isCurated: boolean('is_curated').notNull().default(false),
  partnerName: varchar('partner_name', { length: 255 }), // Only for curated projects

  // Page 2: Project Details (Editor.js JSON)
  projectDetails: jsonb('project_details'), // Editor.js output
  teamIntroduction: jsonb('team_introduction'), // Editor.js output

  // Page 3: Team Introduction (Editor.js JSON)
  teamPublicProfileLinks: text('team_public_profile_links'), // Comma-separated or JSON string of profile links

  // Page 4: Fundraising
  fundraiseHistory: jsonb('fundraise_history'), // Editor.js output
  totalRaisedUsd: decimal('total_raised_usd', { precision: 18, scale: 2 }).notNull().default('0.00'),

  // Page 5: Community & Social
  officialTwitter: varchar('official_twitter', { length: 2048 }).notNull(),
  discordUrl: varchar('discord_url', { length: 2048 }),
  telegram: varchar('telegram', { length: 2048 }),
  farcaster: varchar('farcaster', { length: 2048 }),
  communitySizeEngagement: jsonb('community_size_engagement'), // Editor.js output

  // Page 6: Tokenomics (Editor.js JSON)
  tokenomicsDetails: jsonb('tokenomics_details'), // Editor.js output

  // Page 7: Security (handled in project_audits table)
  securityAuditsGeneralInfo: jsonb('security_audits_general_info'), // Editor.js output

  // Page 8: Token Information
  tokenName: varchar('token_name', { length: 255 }).notNull(),
  tokenSymbol: varchar('token_symbol', { length: 50 }).notNull(),
  tokenContractAddress: varchar('token_contract_address', { length: 255 }).notNull(),
  totalTokenSupply: decimal('total_token_supply', { precision: 38, scale: 0 }).notNull(),
  tokenLaunchMarketCapUsd: decimal('token_launch_market_cap_usd', { precision: 18, scale: 2 }).notNull(),
  tokenThumbnailImageUrl: varchar('token_thumbnail_image_url', { length: 2048 }),

  // Page 9: TGE Details
  tgeType: tgeTypeEnum('tge_type').notNull(),
  allocationTotalSupplyCaishen: decimal('allocation_total_supply_caishen', { precision: 5, scale: 2 }).notNull(),
  allocationCirculatingSupplyCaishen: decimal('allocation_circulating_supply_caishen', {
    precision: 5,
    scale: 2,
  }).notNull(),
  acceptedCurrenciesForPairing: varchar('accepted_currencies_for_pairing', { length: 255 }).notNull(),
  tgePriceUsdc: decimal('tge_price_usdc', { precision: 18, scale: 9 }).notNull(),
  exclusiveTradingPeriodHours: integer('exclusive_trading_period_hours').notNull(),
  expectedTgeLaunchDate: date('expected_tge_launch_date').notNull(),
  liquidityActivationDate: date('liquidity_activation_date').notNull(),
  minAmountPerTradeSol: decimal('min_amount_per_trade_sol', { precision: 18, scale: 9 }).notNull(),
  maxAmountPerTradeSol: decimal('max_amount_per_trade_sol', { precision: 18, scale: 9 }).notNull(),

  // Page 10: Marketing (Editor.js JSON)
  preLaunchMarketingStrategy: jsonb('pre_launch_marketing_strategy'), // Editor.js output

  // Page 11: Banner & Header (Editor.js JSON)
  headerDescription: jsonb('header_description'), // Editor.js output
  bannerBackgroundImageUrl: varchar('banner_background_image_url', { length: 2048 }),

  // Pool Information
  poolAddress: varchar('pool_address', { length: 255 }).unique(),

  // Token Information (after token creation)
  tokenId: varchar('token_id', { length: 255 }).unique(), // Links to token in Firebase

  // Form Progress & Status
  currentFormStep: integer('current_form_step').notNull().default(1),
  submissionStatus: submissionStatusEnum('submission_status').notNull().default('draft'),

  // Post-Approval Fields
  providedWalletAddress: varchar('provided_wallet_address', { length: 255 }), // Optional wallet address provided after approval

  // Timestamps
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
})

// Project categories table
export const projectCategories = pgTable('project_categories', {
  categoryId: serial('category_id').primaryKey(),
  categoryName: varchar('category_name', { length: 100 }).notNull().unique(),
})

// Junction table for project categories (many-to-many)
export const projectHasCategories = pgTable(
  'project_has_categories',
  {
    projectId: uuid('project_id').notNull(),
    categoryId: integer('category_id').notNull(),
  },
  (table) => ({
    pk: { primaryKey: [table.projectId, table.categoryId] },
  }),
)

// Project audits table
export const projectAudits = pgTable('project_audits', {
  auditId: uuid('audit_id').primaryKey().defaultRandom(),
  projectId: uuid('project_id').notNull(),
  auditDate: date('audit_date'),
  auditingFirm: varchar('auditing_firm', { length: 255 }),
  auditReportUrl: varchar('audit_report_url', { length: 2048 }),
  auditingFirmLogoUrl: varchar('auditing_firm_logo_url', { length: 2048 }),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
})

// Status history table for audit trail
export const projectStatusHistory = pgTable('project_status_history', {
  historyId: uuid('history_id').primaryKey().defaultRandom(),
  projectId: uuid('project_id').notNull(),
  previousStatus: submissionStatusEnum('previous_status'),
  newStatus: submissionStatusEnum('new_status').notNull(),
  changedBy: text('changed_by').notNull(), // User ID who made the change
  comment: text('comment'), // Optional comment for status change
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
})

// Relations
export const projectsRelations = relations(projects, ({ many }) => ({
  categories: many(projectHasCategories),
  audits: many(projectAudits),
  statusHistory: many(projectStatusHistory),
}))

export const projectCategoriesRelations = relations(projectCategories, ({ many }) => ({
  projects: many(projectHasCategories),
}))

export const projectHasCategoriesRelations = relations(projectHasCategories, ({ one }) => ({
  project: one(projects, {
    fields: [projectHasCategories.projectId],
    references: [projects.projectId],
  }),
  category: one(projectCategories, {
    fields: [projectHasCategories.categoryId],
    references: [projectCategories.categoryId],
  }),
}))

export const projectAuditsRelations = relations(projectAudits, ({ one }) => ({
  project: one(projects, {
    fields: [projectAudits.projectId],
    references: [projects.projectId],
  }),
}))

export const projectStatusHistoryRelations = relations(projectStatusHistory, ({ one }) => ({
  project: one(projects, {
    fields: [projectStatusHistory.projectId],
    references: [projects.projectId],
  }),
}))

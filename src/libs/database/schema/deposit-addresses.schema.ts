import { pgTable, text, uuid, timestamp, numeric, pgEnum, boolean } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'

import { projects } from './projects.schema'

// Enums for the schema
export const paymentStatusEnum = pgEnum('payment_status', ['pending', 'swept'])

// Deposit addresses table
export const depositAddresses = pgTable('deposit_addresses', {
  id: uuid('id').primaryKey().defaultRandom(),

  // Project information
  projectId: uuid('project_id')
    .notNull()
    .unique()
    .references(() => projects.projectId),

  // EVM address information
  evmPublicAddress: text('evm_public_address').notNull().unique(),
  evmEncryptedPrivateKey: text('evm_encrypted_private_key').notNull(),

  // Solana address information
  solanaPublicAddress: text('solana_public_address').notNull().unique(),
  solanaEncryptedPrivateKey: text('solana_encrypted_private_key').notNull(),

  // Launch configuration
  paymentStatus: paymentStatusEnum('payment_status').notNull().default('pending'),
  amountReceived: numeric('amount_received', { precision: 20, scale: 6 }).default('0'),
  launchFeePaid: boolean('launch_fee_paid').notNull().default(false),

  // Transaction information
  evmDepositTxHash: text('evm_deposit_tx_hash'),
  solanaDepositTxHash: text('solana_deposit_tx_hash'),
  evmSweepTxHash: text('evm_sweep_tx_hash'),
  solanaSweepTxHash: text('solana_sweep_tx_hash'),

  // Metadata
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),

  // Sweep tracking
  lastSweptAt: timestamp('last_swept_at', { withTimezone: true }),
  sweptToCexAddress: text('swept_to_cex_address'),
})

// Zod schemas for validation
export const insertDepositAddressSchema = createInsertSchema(depositAddresses)

export const selectDepositAddressSchema = createSelectSchema(depositAddresses)

// Additional validation schemas if needed
export const amountValidationSchema = z.string().regex(/^\d+(\.\d{1,6})?$/, 'Invalid amount format')

export type DepositAddress = typeof depositAddresses.$inferSelect
export type NewDepositAddress = typeof depositAddresses.$inferInsert

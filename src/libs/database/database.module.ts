import { DrizzlePGModule } from '@knaadh/nestjs-drizzle-pg'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import * as schema from './schema'

@Module({
  imports: [
    DrizzlePGModule.registerAsync({
      tag: 'DB',
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const databaseUrl = configService.get<string>('DATABASE_URL')

        return {
          pg: {
            connection: 'client',
            config: {
              connectionString: databaseUrl,
              ssl: {
                rejectUnauthorized: false,
              },
            },
          },
          config: {
            schema: { ...schema },
            logger: process.env.NODE_ENV !== 'production',
          },
        }
      },
    }),
  ],
  exports: [DrizzlePGModule],
})
export class DatabaseModule {}

import axios from 'axios'

import { isObject } from './predicate/is-object'

type ObjectWithMessage = { message: string } & { [key: string]: unknown }

const isObjectWithMessage = (toBeDetermined: unknown): toBeDetermined is ObjectWithMessage =>
  isObject(toBeDetermined) && !!(toBeDetermined as ObjectWithMessage).message

export const getErrorMessage = (error: unknown, defaultMessage?: string): string => {
  if (axios.isAxiosError(error)) {
    if (error.response?.data && isObjectWithMessage(error.response.data)) {
      return error.response.data.message
    }

    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  if (error instanceof Error || isObjectWithMessage(error)) {
    return error.message
  }

  return defaultMessage || 'Error occurred'
}

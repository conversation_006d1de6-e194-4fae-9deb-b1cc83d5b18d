import { Big, type BigValue } from '@/libs/big-number'

export const formatAmount = (amount: BigValue, decimals?: number, toFixed?: boolean): string => {
  try {
    if (decimals) {
      const fixed = Big(amount).toFixed(decimals)

      return Big(fixed)
        .plus(0)
        .toFormat(toFixed ? decimals : undefined)
    }

    return Big(amount).toFormat(decimals)
  } catch (e) {
    return '0'
  }
}

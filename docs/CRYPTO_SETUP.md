# Crypto Service Setup

This document explains how to set up secure private key encryption using the crypto service.

## Overview

The crypto service uses **AES-256-GCM encryption** with a secret key from environment variables to securely encrypt and decrypt private keys for deposit addresses.

### Security Features

- **AES-256-GCM**: Military-grade encryption with authentication
- **Key Derivation**: Uses scrypt for secure key derivation from your secret
- **Random Salt & IV**: Each encryption uses unique salt and initialization vector
- **Authentication Tag**: Prevents tampering with encrypted data

## Setup Instructions

### 1. Generate a Secret Key

Run the key generation script:

```bash
node scripts/generate-crypto-key.js
```

This will output something like:
```
🔐 Generated secure crypto secret key:

CRYPTO_SECRET_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

⚠️  Important: Store this key securely and do not commit it to version control!
💡 Add this to your .env file and keep it secret.
```

### 2. Add to Environment Variables

Add the generated key to your `.env` file:

```env
# Crypto Service Configuration
CRYPTO_SECRET_KEY=your_generated_key_here
```

**Important Security Notes:**
- ⚠️ Never commit this key to version control
- 🔒 Store it securely (password manager, secure notes)
- 🔄 Rotate the key periodically in production
- 📝 Keep backups in secure locations

### 3. Verify Configuration

The service will automatically validate the configuration on startup:

- ✅ Key must be present
- ✅ Key must be at least 32 characters long
- ✅ Service will log successful initialization

## How It Works

### Encryption Process

1. **Key Derivation**: Your secret key is combined with a random salt using scrypt
2. **Random IV**: A unique initialization vector is generated
3. **AES-256-GCM**: The private key is encrypted using the derived key and IV
4. **Authentication**: An authentication tag is generated to prevent tampering
5. **Storage**: Salt, IV, auth tag, and encrypted data are combined and base64-encoded

### Decryption Process

1. **Parse**: The encrypted data is parsed to extract salt, IV, auth tag, and ciphertext
2. **Key Derivation**: The same key derivation process recreates the encryption key
3. **Verification**: The authentication tag is verified to ensure data integrity
4. **Decryption**: The private key is decrypted using AES-256-GCM

### Data Format

Encrypted private keys are stored as base64-encoded JSON:

```json
{
  "salt": "hex-encoded-salt",
  "iv": "hex-encoded-iv", 
  "authTag": "hex-encoded-auth-tag",
  "encrypted": "hex-encoded-ciphertext"
}
```

## Production Considerations

### Security Best Practices

1. **Key Management**:
   - Use a secure key management system in production
   - Consider rotating keys periodically
   - Implement key backup and recovery procedures

2. **Environment Variables**:
   - Use secure secret management (AWS Secrets Manager, etc.)
   - Avoid plain text files in production
   - Implement proper access controls

3. **Monitoring**:
   - Log encryption/decryption operations (without sensitive data)
   - Monitor for failed decryption attempts
   - Set up alerts for configuration issues

### Migration from Base64

If you have existing addresses encrypted with base64:

1. **Backup**: Create a full database backup
2. **Migration Script**: Create a script to re-encrypt existing keys
3. **Testing**: Test the migration in a staging environment
4. **Deployment**: Deploy with the new crypto service enabled

## API Usage

The crypto service is automatically used by:

- **Deposit Address Service**: Encrypts new private keys
- **Sweep Service**: Decrypts private keys for transactions

### Validation Endpoint

You can validate the crypto configuration:

```typescript
const validation = cryptoService.validateConfiguration();
if (!validation.valid) {
  console.error('Crypto configuration errors:', validation.errors);
}
```

## Troubleshooting

### Common Issues

1. **"CRYPTO_SECRET_KEY environment variable is required"**
   - Ensure the environment variable is set
   - Check for typos in the variable name

2. **"CRYPTO_SECRET_KEY should be at least 32 characters long"**
   - Generate a new key using the provided script
   - Ensure the full key is copied to the environment variable

3. **"Decryption failed"**
   - The secret key may have changed
   - The encrypted data may be corrupted
   - Check that you're using the correct key

### Debug Mode

For debugging, check the service logs:

```
[CryptoService] Initialized crypto service with AES-256-GCM encryption
[CryptoService] Successfully encrypted private key with AES-256-GCM
[CryptoService] Successfully decrypted private key with AES-256-GCM
```

## Security Considerations

### Strengths

- ✅ Strong encryption (AES-256-GCM)
- ✅ Secure key derivation (scrypt)
- ✅ Authenticated encryption
- ✅ Random salts prevent rainbow table attacks
- ✅ Each encryption is unique

### Limitations

- ⚠️ Secret key must be stored somewhere
- ⚠️ Single point of failure if key is lost
- ⚠️ Requires secure key management in production

### Recommendations

For production deployments, consider:

1. **Cloud KMS**: AWS KMS, Azure Key Vault, Google Cloud KMS
2. **Hardware Security Modules (HSMs)**
3. **Key rotation strategies**
4. **Multi-party key management**
5. **Secure backup and recovery procedures**

## Support

If you encounter issues or need help with the crypto service setup, please refer to the project documentation or contact the development team. 
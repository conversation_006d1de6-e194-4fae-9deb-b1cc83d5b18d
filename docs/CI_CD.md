# CI/CD Pipeline Documentation

This project uses GitHub Actions for continuous integration and deployment. The pipeline ensures code quality and automates deployments to different environments.

## Workflows Overview

### 1. Pull Request Checks (`pr-checks.yml`)
**Triggers:** Pull requests to `main` or `production` branches
**Purpose:** Run quality checks on PRs without deployment

**Jobs:**
- **Quality Assurance**: Runs type checking, linting, formatting checks, and build
- **Security Check**: Runs npm audit and checks for outdated dependencies

**Features:**
- Skips draft PRs (unless marked as ready for review)
- Includes security and dependency checks
- Continues on test errors (non-blocking)

### 2. Status Checks (`status-checks.yml`)
**Triggers:** Pull requests and pushes to `main` or `production` branches
**Purpose:** Aggregated status check that can be used as a required check

**Jobs:**
- **All Required Checks**: Runs all quality checks in a single job
- Provides a summary of passed checks
- Can be set as a required status check in GitHub branch protection

### 3. Testnet Deployment (`main.yml`)
**Triggers:** 
- Pushes to `main` branch (deploys)
- Pull requests to `main` branch (quality checks only)

**Jobs:**
- **Quality Assurance**: Same checks as PR workflow
- **Build and Deploy**: Only runs on pushes to main
  - Runs database migrations
  - Builds and pushes Docker image to GHCR
  - Deploys to testnet ECS cluster

### 4. Production Deployment (`production.yml`)
**Triggers:**
- Pushes to `production` branch (deploys)
- Pull requests to `production` branch (quality checks only)

**Jobs:**
- **Quality Assurance**: Same checks as other workflows
- **Build and Deploy**: Only runs on pushes to production
  - Builds and pushes Docker image to GHCR
  - Deploys to production ECS cluster

## Quality Checks

All workflows run the following quality checks:

1. **Type Checking**: `npm run type-check`
   - Ensures TypeScript compilation without errors
   
2. **Linting**: `npm run lint:ci`
   - Runs ESLint with zero warnings tolerance
   
3. **Code Formatting**: `npm run format:check`
   - Ensures code follows Prettier formatting rules
   
4. **Build**: `npm run build`
   - Verifies the application builds successfully

5. **Tests**: `npm test --if-present` (if test script exists)
   - Runs unit tests (currently optional)

## Security Checks

The PR checks workflow also includes:

1. **npm audit**: Checks for known security vulnerabilities
2. **Dependency check**: Lists outdated dependencies

## Branch Protection

To ensure code quality, consider setting up branch protection rules:

### For `main` branch:
- Require status checks to pass before merging
- Require "All Required Checks" from `status-checks.yml`
- Require up-to-date branches before merging
- Require pull request reviews

### For `production` branch:
- Same as main branch
- Consider requiring additional approvals
- Restrict pushes to specific users/teams

## Environment Variables and Secrets

The workflows use the following secrets:

- `DATABASE_URL`: Database connection string for migrations
- `AWS_IAM_ROLE_ARN`: AWS role for ECS deployments
- `GITHUB_TOKEN`: Automatically provided by GitHub

## Deployment Flow

1. **Development**: Create feature branch from `main`
2. **Pull Request**: Open PR to `main` → triggers quality checks
3. **Review**: Code review and approval
4. **Merge to main**: Triggers testnet deployment
5. **Production**: Merge `main` to `production` → triggers production deployment

## Troubleshooting

### Common Issues:

1. **Linting failures**: Run `npm run lint:fix` locally
2. **Formatting issues**: Run `npm run format` locally
3. **Type errors**: Fix TypeScript errors shown in the logs
4. **Build failures**: Check for missing dependencies or syntax errors

### Local Development:

Run all checks locally before pushing:
```bash
npm run check  # Runs type-check, lint:check, and format:check
npm run build  # Verify build works
```

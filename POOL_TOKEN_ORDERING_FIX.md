# Pool Token Ordering Fix for Solana

## Problem

The pool creation service had an issue with token ordering on Solana. The problem was:

1. **Token Ordering**: On Solana (like other AMMs), tokens are ordered by their address (token0 < token1)
2. **Price Calculation**: The TGE price is always given as "USDC price per project token" (e.g., 1.5 means 1 PROJECT_TOKEN = 1.5 USDC)
3. **Missing Logic**: The Solana pool creation didn't account for which position USDC would be in (token0 vs token1)
4. **Price Adjustment**: When USDC is token1 instead of token0, we need to use the reciprocal of the TGE price

## Solution

### 1. Updated Price Utilities (`src/modules/pool/utils/price-utils.ts`)

- **Enhanced `tgePriceToSqrtPriceX96`**: Now takes token addresses and properly handles token ordering
- **Added `calculateInitialPrice`**: Helper function to calculate the correct initial price based on token ordering
- **Improved token sorting**: Uses the existing `sortTokenAddresses` function consistently

### 2. Fixed EVM Pool Creation (`src/modules/pool/pool.service.ts`)

- **Proper Token Ordering**: Now uses `sortTokenAddresses` to determine correct token0/token1
- **Consistent Pool Checks**: Uses sorted addresses when checking for existing pools
- **Updated Price Calculation**: Passes token addresses to `tgePriceToSqrtPriceX96` for proper price adjustment

### 3. Fixed Solana Pool Creation (`src/modules/pool/pool.service.ts`)

- **Token Sorting**: Determines correct token ordering using `sortTokenAddresses`
- **Price Adjustment**: Calculates the correct initial price based on USDC position:
  - If USDC is token0: `initialPrice = 1 / tgePrice` (PROJECT_TOKEN per USDC)
  - If USDC is token1: `initialPrice = tgePrice` (USDC per PROJECT_TOKEN)
- **Mint Assignment**: Assigns `mint1Info` and `mint2Info` based on sorted token order
- **Logging**: Added detailed logging to track token ordering decisions

## Key Changes

### Price Calculation Logic

```typescript
// Before: Always used tgePrice directly
initialPrice: new Decimal(config.tgePrice)

// After: Adjust based on token ordering
let initialPrice: number
if (isUsdcToken0) {
  // token0 = USDC, token1 = PROJECT_TOKEN
  // We want PROJECT_TOKEN per USDC = 1/tgePrice
  initialPrice = 1 / config.tgePrice
} else {
  // token0 = PROJECT_TOKEN, token1 = USDC  
  // We want USDC per PROJECT_TOKEN = tgePrice
  initialPrice = config.tgePrice
}
```

### Token Ordering

```typescript
// Sort tokens to determine correct ordering (token0 < token1)
const { token0, token1, isUsdcToken0 } = sortTokenAddresses(config.usdcAddress, config.projectTokenAddress)

// Assign mint1 and mint2 based on sorted order
const mint1Info = isUsdcToken0 ? usdcTokenInfo : projectTokenInfo
const mint2Info = isUsdcToken0 ? projectTokenInfo : usdcTokenInfo
```

## Examples

### Example 1: USDC is token0 (USDC address < Project token address)
- TGE Price: 1.5 (1 PROJECT_TOKEN = 1.5 USDC)
- Token ordering: token0 = USDC, token1 = PROJECT_TOKEN
- Initial price: 1/1.5 = 0.667 (PROJECT_TOKEN per USDC)

### Example 2: USDC is token1 (USDC address > Project token address)  
- TGE Price: 1.5 (1 PROJECT_TOKEN = 1.5 USDC)
- Token ordering: token0 = PROJECT_TOKEN, token1 = USDC
- Initial price: 1.5 (USDC per PROJECT_TOKEN)

## Testing

The changes have been tested with:
- TypeScript compilation (`npm run type-check`)
- Build process (`npm run build`)
- Both pass successfully

## Impact

This fix ensures that:
1. **Solana pools** are created with the correct initial price regardless of token address ordering
2. **EVM pools** also use proper token ordering (fixing a similar issue)
3. **Price consistency** across both Solana and EVM chains
4. **Proper AMM behavior** where token0 < token1 always holds true

The fix is backward compatible and doesn't change the API - it only fixes the internal price calculation logic.
